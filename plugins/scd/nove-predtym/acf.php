<?php
add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}

	acf_add_local_field_group( array(
	'key' => 'group_60e5a78ac97d4',
	'title' => '8-bit',
	'fields' => array(
		array(
			'key' => 'field_61156e8c1d0d5',
			'label' => 'Hry',
			'name' => 'hry',
			'aria-label' => '',
			'type' => 'relationship',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'scd_hra',
			),
			'taxonomy' => '',
			'filters' => array(
				0 => 'search',
			),
			'elements' => '',
			'min' => '',
			'max' => '',
			'return_format' => 'object',
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60e5a78adf0d5',
			'label' => 'Gameplay',
			'name' => 'gameplay',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '66',
				'class' => '',
				'id' => '',
			),
			'collapsed' => 'field_60d34d6e37fdc',
			'min' => 2,
			'max' => 2,
			'layout' => 'table',
			'button_label' => 'Pridať video',
			'sub_fields' => array(
				array(
					'key' => 'field_60e5a78ae70ac',
					'label' => 'Video',
					'name' => 'video',
					'aria-label' => '',
					'type' => 'group',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'layout' => 'block',
					'sub_fields' => array(
						array(
							'key' => 'field_60e5a78af0df4',
							'label' => 'Názov videa',
							'name' => 'nazov_videa',
							'aria-label' => '',
							'type' => 'text',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'default_value' => '',
							'placeholder' => '',
							'prepend' => '',
							'append' => '',
							'maxlength' => '',
						),
						array(
							'key' => 'field_60e5a78af101d',
							'label' => 'Youtube linka',
							'name' => 'youtube_linka',
							'aria-label' => '',
							'type' => 'url',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'default_value' => '',
							'placeholder' => '',
						),
					),
					'parent_repeater' => 'field_60e5a78adf0d5',
				),
			),
			'rows_per_page' => 20,
		),
		array(
			'key' => 'field_60e5a78adf132',
			'label' => 'Gameplay YT kanál',
			'name' => 'gameplay_kanal',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
		),
		array(
			'key' => 'field_6115700fcda3e',
			'label' => 'Rozhovory',
			'name' => 'rozhovory',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '66',
				'class' => '',
				'id' => '',
			),
			'collapsed' => 'field_60d34d6e37fdc',
			'min' => 2,
			'max' => 2,
			'layout' => 'table',
			'button_label' => 'Pridať video',
			'sub_fields' => array(
				array(
					'key' => 'field_6115700fcda3f',
					'label' => 'Video',
					'name' => 'video',
					'aria-label' => '',
					'type' => 'group',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'layout' => 'block',
					'sub_fields' => array(
						array(
							'key' => 'field_6115700fcda40',
							'label' => 'Názov videa',
							'name' => 'nazov_videa',
							'aria-label' => '',
							'type' => 'text',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'default_value' => '',
							'placeholder' => '',
							'prepend' => '',
							'append' => '',
							'maxlength' => '',
						),
						array(
							'key' => 'field_6115700fcda41',
							'label' => 'Youtube linka',
							'name' => 'youtube_linka',
							'aria-label' => '',
							'type' => 'url',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'default_value' => '',
							'placeholder' => '',
						),
					),
					'parent_repeater' => 'field_6115700fcda3e',
				),
			),
			'rows_per_page' => 20,
		),
		array(
			'key' => 'field_61157047cda42',
			'label' => 'Rozhovory YT kanál',
			'name' => 'rozhovory_kanal',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
		),
		array(
			'key' => 'field_611570f9d860a',
			'label' => 'Obaly',
			'name' => 'obaly',
			'aria-label' => '',
			'type' => 'gallery',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'return_format' => 'array',
			'preview_size' => 'medium',
			'insert' => 'prepend',
			'library' => 'all',
			'min' => '',
			'max' => '',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => '',
			'mime_types' => '',
		),
		array(
			'key' => 'field_61157134d860b',
			'label' => 'Hardvér',
			'name' => 'hardver',
			'aria-label' => '',
			'type' => 'gallery',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'return_format' => 'array',
			'preview_size' => 'medium',
			'insert' => 'prepend',
			'library' => 'all',
			'min' => '',
			'max' => '',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => '',
			'mime_types' => '',
		),
		array(
			'key' => 'field_61157178d860c',
			'label' => 'Časopisy',
			'name' => 'casopisy',
			'aria-label' => '',
			'type' => 'gallery',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'return_format' => 'array',
			'preview_size' => 'medium',
			'insert' => 'prepend',
			'library' => 'all',
			'min' => '',
			'max' => '',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => '',
			'mime_types' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => '3571',
			),
		),
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => '382921',
			),
		),
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => '382921',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => array(
		0 => 'permalink',
		1 => 'discussion',
		2 => 'comments',
		3 => 'slug',
		4 => 'format',
		5 => 'tags',
	),
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );

	acf_add_local_field_group( array(
	'key' => 'group_609cd2e524137',
	'title' => 'Autor',
	'fields' => array(
		array(
			'key' => 'field_609cd2eab28cb',
			'label' => 'Porotca',
			'name' => 'porotca',
			'aria-label' => '',
			'type' => 'true_false',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'message' => '',
			'default_value' => 0,
			'ui' => 0,
			'ui_on_text' => '',
			'ui_off_text' => '',
		),
		array(
			'key' => 'field_609cd366b28cc',
			'label' => 'Krajina',
			'name' => 'krajina',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
		array(
			'key' => 'field_60f53b446d3cc',
			'label' => 'ID',
			'name' => 'id',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'scd_autor',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => array(
		0 => 'discussion',
		1 => 'comments',
		2 => 'format',
		3 => 'tags',
	),
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );

	acf_add_local_field_group( array(
	'key' => 'group_60740e7bae4ed',
	'title' => 'Článok',
	'fields' => array(
		array(
			'key' => 'field_606fffde87974',
			'label' => 'Autori',
			'name' => 'autori',
			'aria-label' => '',
			'type' => 'relationship',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'scd_autor',
			),
			'taxonomy' => '',
			'filters' => array(
				0 => 'search',
			),
			'elements' => '',
			'min' => '',
			'max' => '',
			'return_format' => 'object',
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_608924bb8118c',
			'label' => 'Perex',
			'name' => 'perex',
			'aria-label' => '',
			'type' => 'textarea',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'maxlength' => '',
			'rows' => '',
			'new_lines' => 'br',
		),
		array(
			'key' => 'field_608bcba9b41ee',
			'label' => 'Vydanie Designum',
			'name' => 'vydanie_designum',
			'aria-label' => '',
			'type' => 'post_object',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'scd_designum',
			),
			'taxonomy' => '',
			'allow_null' => 1,
			'multiple' => 0,
			'return_format' => 'object',
			'ui' => 1,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_607041bb5c05a',
			'label' => 'Zdroj',
			'name' => 'zdroj',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
		array(
			'key' => 'field_60757826458b9',
			'label' => 'Zdroj URL',
			'name' => 'zdroj_url',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
		),
		array(
			'key' => 'field_615c580f37291',
			'label' => 'eDesignum',
			'name' => 'edesignum',
			'aria-label' => '',
			'type' => 'true_false',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'message' => '',
			'default_value' => 0,
			'ui' => 0,
			'ui_on_text' => '',
			'ui_off_text' => '',
		),
		array(
			'key' => 'field_60ae1d04611f5',
			'label' => 'Súvisiace diela',
			'name' => 'suvisiace_diela',
			'aria-label' => '',
			'type' => 'relationship',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'scd_dielo_smd',
				1 => 'scd_dielo_ncd',
			),
			'taxonomy' => '',
			'filters' => array(
				0 => 'search',
			),
			'elements' => '',
			'min' => '',
			'max' => 3,
			'return_format' => 'object',
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60740fb7fb5e8',
			'label' => 'Súvisiaca výstava',
			'name' => 'vystavy',
			'aria-label' => '',
			'type' => 'post_object',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'scd_vystava',
			),
			'taxonomy' => '',
			'allow_null' => 1,
			'multiple' => 0,
			'return_format' => 'object',
			'ui' => 1,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60ae1d99611f6',
			'label' => 'Súvisiace publikácie',
			'name' => 'suvisiace_publikacie',
			'aria-label' => '',
			'type' => 'relationship',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'scd_publikacia',
				1 => 'scd_pub_kniznice',
				2 => 'scd_designum',
			),
			'taxonomy' => '',
			'filters' => array(
				0 => 'search',
			),
			'elements' => '',
			'min' => '',
			'max' => 4,
			'return_format' => 'object',
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60dcc3baa184b',
			'label' => 'Galéria',
			'name' => 'galeria',
			'aria-label' => '',
			'type' => 'gallery',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'return_format' => 'array',
			'preview_size' => 'thumbnail',
			'insert' => 'prepend',
			'library' => 'all',
			'min' => '',
			'max' => '',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => '',
			'mime_types' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'post',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => array(
		0 => 'discussion',
		1 => 'comments',
		2 => 'format',
	),
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );

	acf_add_local_field_group( array(
	'key' => 'group_60d2fefe12a07',
	'title' => 'Designum',
	'fields' => array(
		array(
			'key' => 'field_60d2fefe228d8',
			'label' => 'Autor',
			'name' => 'autori',
			'aria-label' => '',
			'type' => 'relationship',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'scd_autor',
				1 => 'scd_osobnost_dizajnu',
			),
			'taxonomy' => '',
			'filters' => array(
				0 => 'search',
			),
			'elements' => '',
			'min' => '',
			'max' => '',
			'return_format' => 'object',
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60d2fefe22917',
			'label' => 'Dátum vydania',
			'name' => 'datum_vydania',
			'aria-label' => '',
			'type' => 'number',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'min' => '',
			'max' => '',
			'step' => '',
		),
		array(
			'key' => 'field_60d2fefe2292d',
			'label' => 'Vydavateľstvo',
			'name' => 'vydavatelstvo',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'scd_vydavatelstvo',
			'field_type' => 'select',
			'allow_null' => 1,
			'add_term' => 1,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'object',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60d2fefe229d9',
			'label' => 'Dizajnéri v publikácii',
			'name' => 'dizajneri_v_publikacii',
			'aria-label' => '',
			'type' => 'relationship',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'scd_osobnost_dizajnu',
			),
			'taxonomy' => '',
			'filters' => array(
				0 => 'search',
			),
			'elements' => '',
			'min' => '',
			'max' => '',
			'return_format' => 'object',
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60eebcccaf65c',
			'label' => 'Cena',
			'name' => 'cena',
			'aria-label' => '',
			'type' => 'number',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '€',
			'min' => '',
			'max' => '',
			'step' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'scd_designum',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => array(
		0 => 'discussion',
		1 => 'comments',
		2 => 'format',
		3 => 'tags',
	),
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );

	acf_add_local_field_group( array(
	'key' => 'group_60ace1bfae8c5',
	'title' => 'Dielo NCD',
	'fields' => array(
		array(
			'key' => 'field_60ace1bfc31ab',
			'label' => 'ID',
			'name' => 'id',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '25',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
		array(
			'key' => 'field_60ace1bfc327c',
			'label' => 'Datovanie',
			'name' => 'datovanie',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '25',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
		array(
			'key' => 'field_60ace1bfc31c5',
			'label' => 'Značky',
			'name' => 'znacky',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'post_tag',
			'field_type' => 'multi_select',
			'allow_null' => 1,
			'add_term' => 1,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'object',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60ace1bfc31ff',
			'label' => 'Dizajnér + rola',
			'name' => 'dizajner_rola',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'collapsed' => '',
			'min' => 0,
			'max' => 0,
			'layout' => 'table',
			'button_label' => 'Pridať riadok',
			'sub_fields' => array(
				array(
					'key' => 'field_60f980ca990a4',
					'label' => 'Dizajnér',
					'name' => 'dizajner',
					'aria-label' => '',
					'type' => 'post_object',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '50',
						'class' => '',
						'id' => '',
					),
					'post_type' => array(
						0 => 'scd_osobnost_dizajnu',
					),
					'taxonomy' => array(
						0 => 'scd_osobnost_typ:osobnosti-dizajnu',
						1 => 'scd_osobnost_typ:design-personalities',
					),
					'allow_null' => 0,
					'multiple' => 0,
					'return_format' => 'object',
					'ui' => 1,
					'bidirectional_target' => array(
					),
					'parent_repeater' => 'field_60ace1bfc31ff',
				),
				array(
					'key' => 'field_60f98124990a5',
					'label' => 'Rola',
					'name' => 'rola',
					'aria-label' => '',
					'type' => 'taxonomy',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '50',
						'class' => '',
						'id' => '',
					),
					'taxonomy' => 'scd_rola',
					'field_type' => 'select',
					'allow_null' => 0,
					'add_term' => 1,
					'save_terms' => 0,
					'load_terms' => 0,
					'return_format' => 'object',
					'multiple' => 0,
					'bidirectional_target' => array(
					),
					'parent_repeater' => 'field_60ace1bfc31ff',
				),
			),
			'rows_per_page' => 20,
		),
		array(
			'key' => 'field_60f9910a6697c',
			'label' => 'Výrobca + rola',
			'name' => 'vyrobca_rola',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'collapsed' => '',
			'min' => 0,
			'max' => 0,
			'layout' => 'table',
			'button_label' => 'Pridať riadok',
			'sub_fields' => array(
				array(
					'key' => 'field_60f9910a6697d',
					'label' => 'Výrobca',
					'name' => 'vyrobca',
					'aria-label' => '',
					'type' => 'post_object',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '50',
						'class' => '',
						'id' => '',
					),
					'post_type' => array(
						0 => 'scd_osobnost_dizajnu',
					),
					'taxonomy' => array(
						0 => 'scd_osobnost_typ:vyrobcovia',
						1 => 'scd_osobnost_typ:producers',
					),
					'allow_null' => 0,
					'multiple' => 0,
					'return_format' => 'object',
					'ui' => 1,
					'bidirectional_target' => array(
					),
					'parent_repeater' => 'field_60f9910a6697c',
				),
				array(
					'key' => 'field_60f9910a6697e',
					'label' => 'Rola',
					'name' => 'rola',
					'aria-label' => '',
					'type' => 'taxonomy',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '50',
						'class' => '',
						'id' => '',
					),
					'taxonomy' => 'scd_rola',
					'field_type' => 'select',
					'allow_null' => 0,
					'add_term' => 1,
					'save_terms' => 0,
					'load_terms' => 0,
					'return_format' => 'object',
					'multiple' => 0,
					'bidirectional_target' => array(
					),
					'parent_repeater' => 'field_60f9910a6697c',
				),
			),
			'rows_per_page' => 20,
		),
		array(
			'key' => 'field_60acecc8b1593',
			'label' => 'NCD ročník',
			'name' => 'ncd_rocnik',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'scd_ncd_rocnik',
			'field_type' => 'select',
			'allow_null' => 1,
			'add_term' => 1,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'object',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60aceda3b1594',
			'label' => 'NCD kategória',
			'name' => 'ncd_kategoria',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'scd_ncd_kategoria',
			'field_type' => 'select',
			'allow_null' => 1,
			'add_term' => 1,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'object',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60acf02bb1595',
			'label' => 'Hodnotenie',
			'name' => 'ocenenie',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'scd_ocenenie',
			'field_type' => 'multi_select',
			'allow_null' => 1,
			'add_term' => 1,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'object',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60ace1bfc3404',
			'label' => 'Obrázky',
			'name' => 'obrazky',
			'aria-label' => '',
			'type' => 'gallery',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'return_format' => 'id',
			'preview_size' => 'medium',
			'insert' => 'prepend',
			'library' => 'all',
			'min' => '',
			'max' => '',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => '',
			'mime_types' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'scd_dielo_ncd',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => array(
		0 => 'discussion',
		1 => 'comments',
		2 => 'format',
		3 => 'featured_image',
		4 => 'tags',
	),
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );

	acf_add_local_field_group( array(
	'key' => 'group_60a61888b7ded',
	'title' => 'Dielo SMD',
	'fields' => array(
		array(
			'key' => 'field_60a618d0f0f31',
			'label' => 'ID',
			'name' => 'id',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '25',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
		array(
			'key' => 'field_60a61ca1f0f33',
			'label' => 'Značky',
			'name' => 'znacky',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '25',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'post_tag',
			'field_type' => 'multi_select',
			'allow_null' => 1,
			'add_term' => 1,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'object',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60fd8f328e702',
			'label' => 'Dizajnér + rola',
			'name' => 'dizajner_rola',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'collapsed' => '',
			'min' => 0,
			'max' => 0,
			'layout' => 'table',
			'button_label' => 'Pridať riadok',
			'sub_fields' => array(
				array(
					'key' => 'field_60fd8f328e703',
					'label' => 'Dizajnér',
					'name' => 'dizajner',
					'aria-label' => '',
					'type' => 'post_object',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '50',
						'class' => '',
						'id' => '',
					),
					'post_type' => array(
						0 => 'scd_osobnost_dizajnu',
					),
					'taxonomy' => array(
						0 => 'scd_osobnost_typ:design-personalities',
						1 => 'scd_osobnost_typ:osobnosti-dizajnu',
					),
					'allow_null' => 0,
					'multiple' => 0,
					'return_format' => 'object',
					'ui' => 1,
					'bidirectional_target' => array(
					),
					'parent_repeater' => 'field_60fd8f328e702',
				),
				array(
					'key' => 'field_60fd8f328e704',
					'label' => 'Rola',
					'name' => 'rola',
					'aria-label' => '',
					'type' => 'taxonomy',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '50',
						'class' => '',
						'id' => '',
					),
					'taxonomy' => 'scd_rola',
					'field_type' => 'select',
					'allow_null' => 0,
					'add_term' => 1,
					'save_terms' => 0,
					'load_terms' => 0,
					'return_format' => 'object',
					'multiple' => 0,
					'bidirectional_target' => array(
					),
					'parent_repeater' => 'field_60fd8f328e702',
				),
			),
			'rows_per_page' => 20,
		),
		array(
			'key' => 'field_60fd8f428e705',
			'label' => 'Výrobca + rola',
			'name' => 'vyrobca_rola',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'collapsed' => '',
			'min' => 0,
			'max' => 0,
			'layout' => 'table',
			'button_label' => 'Pridať riadok',
			'sub_fields' => array(
				array(
					'key' => 'field_60fd8f428e706',
					'label' => 'Výrobca',
					'name' => 'vyrobca',
					'aria-label' => '',
					'type' => 'post_object',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '50',
						'class' => '',
						'id' => '',
					),
					'post_type' => array(
						0 => 'scd_osobnost_dizajnu',
					),
					'taxonomy' => array(
						0 => 'scd_osobnost_typ:vyrobcovia',
						1 => 'scd_osobnost_typ:producers',
					),
					'allow_null' => 0,
					'multiple' => 0,
					'return_format' => 'object',
					'ui' => 1,
					'bidirectional_target' => array(
					),
					'parent_repeater' => 'field_60fd8f428e705',
				),
				array(
					'key' => 'field_60fd8f428e707',
					'label' => 'Rola',
					'name' => 'rola',
					'aria-label' => '',
					'type' => 'taxonomy',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '50',
						'class' => '',
						'id' => '',
					),
					'taxonomy' => 'scd_rola',
					'field_type' => 'select',
					'allow_null' => 0,
					'add_term' => 1,
					'save_terms' => 0,
					'load_terms' => 0,
					'return_format' => 'object',
					'multiple' => 0,
					'bidirectional_target' => array(
					),
					'parent_repeater' => 'field_60fd8f428e705',
				),
			),
			'rows_per_page' => 20,
		),
		array(
			'key' => 'field_60a61e11f0f35',
			'label' => 'Datovanie',
			'name' => 'datovanie',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '25',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
		array(
			'key' => 'field_60a61e22f0f36',
			'label' => 'Materiál',
			'name' => 'material',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '25',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'scd_material',
			'field_type' => 'multi_select',
			'allow_null' => 1,
			'add_term' => 1,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'object',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60a61e74f0f37',
			'label' => 'Technika',
			'name' => 'technika',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '25',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'scd_technika',
			'field_type' => 'multi_select',
			'allow_null' => 1,
			'add_term' => 1,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'object',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60a6662037757',
			'label' => 'Rozmery',
			'name' => 'rozmery',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
		array(
			'key' => 'field_60a61f01f0f38',
			'label' => 'Výška',
			'name' => 'vyska',
			'aria-label' => '',
			'type' => 'number',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '25',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => 'mm',
			'min' => '',
			'max' => '',
			'step' => '',
		),
		array(
			'key' => 'field_60a61f1df0f39',
			'label' => 'Šírka',
			'name' => 'sirka',
			'aria-label' => '',
			'type' => 'number',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '25',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => 'mm',
			'min' => '',
			'max' => '',
			'step' => '',
		),
		array(
			'key' => 'field_60a61f33f0f3a',
			'label' => 'Hĺbka',
			'name' => 'hlbka',
			'aria-label' => '',
			'type' => 'number',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '25',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => 'mm',
			'min' => '',
			'max' => '',
			'step' => '',
		),
		array(
			'key' => 'field_60a61f7df0f3b',
			'label' => 'Obrázky',
			'name' => 'obrazky',
			'aria-label' => '',
			'type' => 'gallery',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'return_format' => 'id',
			'preview_size' => 'medium',
			'insert' => 'prepend',
			'library' => 'all',
			'min' => '',
			'max' => '',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => '',
			'mime_types' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'scd_dielo_smd',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => array(
		0 => 'discussion',
		1 => 'comments',
		2 => 'format',
		3 => 'featured_image',
		4 => 'tags',
	),
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );

	acf_add_local_field_group( array(
	'key' => 'group_60ca0fe5291e0',
	'title' => 'Galéria SATELIT',
	'fields' => array(
		array(
			'key' => 'field_60ca0fe542717',
			'label' => 'Lokalita',
			'name' => 'lokalita',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'scd_lokalita',
			'field_type' => 'select',
			'allow_null' => 1,
			'add_term' => 0,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'object',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60ca0fe542757',
			'label' => 'Rýchle odkazy',
			'name' => 'rychle_odkazy',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'collapsed' => 'field_60801a43fc88f',
			'min' => 0,
			'max' => 0,
			'layout' => 'table',
			'button_label' => 'Pridať odkaz',
			'sub_fields' => array(
				array(
					'key' => 'field_60ca0fe5544e4',
					'label' => 'Odkaz',
					'name' => 'odkaz',
					'aria-label' => '',
					'type' => 'group',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'layout' => 'block',
					'sub_fields' => array(
						array(
							'key' => 'field_60ca0fe559752',
							'label' => 'Text odkazu',
							'name' => 'text_odkazu',
							'aria-label' => '',
							'type' => 'text',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'default_value' => '',
							'placeholder' => '',
							'prepend' => '',
							'append' => '',
							'maxlength' => '',
						),
						array(
							'key' => 'field_60ca0fe55978f',
							'label' => 'Linka odkazu',
							'name' => 'linka_odkazu',
							'aria-label' => '',
							'type' => 'url',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'default_value' => '',
							'placeholder' => '',
						),
						array(
							'key' => 'field_669f863966372',
							'label' => 'Cieľ odkazu',
							'name' => 'target_odkazu',
							'aria-label' => '',
							'type' => 'select',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'choices' => array(
								'____' => 'v tomto okne/tabe',
								'_blank' => 'do nového okna/tabu',
								'_self' => 'tento frame',
								'_parent' => 'rodičovský frame',
								'_top' => 'nove plné okno',
							),
							'default_value' => false,
							'return_format' => 'value',
							'multiple' => 0,
							'allow_null' => 0,
							'ui' => 0,
							'ajax' => 0,
							'placeholder' => '',
						),
					),
					'parent_repeater' => 'field_60ca0fe542757',
				),
			),
			'rows_per_page' => 20,
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => '951',
			),
		),
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => '86019',
			),
		),
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => '86019',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => array(
		0 => 'permalink',
		1 => 'discussion',
		2 => 'comments',
		3 => 'slug',
		4 => 'format',
		5 => 'categories',
		6 => 'tags',
	),
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );

	acf_add_local_field_group( array(
	'key' => 'group_61124624e6a1e',
	'title' => 'Hra',
	'fields' => array(
		array(
			'key' => 'field_6112477a38a1f',
			'label' => 'Dátum vydania',
			'name' => 'datum_vydania',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
		array(
			'key' => 'field_611247b638a20',
			'label' => 'Platforma',
			'name' => 'platforma',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'scd_platforma',
			'field_type' => 'multi_select',
			'allow_null' => 1,
			'add_term' => 1,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'object',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_61124ae638a24',
			'label' => 'Formát média',
			'name' => 'format_media',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'scd_format_media',
			'field_type' => 'multi_select',
			'allow_null' => 1,
			'add_term' => 1,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'object',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_611249f138a23',
			'label' => 'Autori',
			'name' => 'autori',
			'aria-label' => '',
			'type' => 'post_object',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'scd_osobnost_dizajnu',
			),
			'taxonomy' => '',
			'allow_null' => 1,
			'multiple' => 1,
			'return_format' => 'object',
			'ui' => 1,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_611249c138a22',
			'label' => 'Štúdio',
			'name' => 'studio',
			'aria-label' => '',
			'type' => 'post_object',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'scd_osobnost_dizajnu',
			),
			'taxonomy' => '',
			'allow_null' => 1,
			'multiple' => 1,
			'return_format' => 'object',
			'ui' => 1,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_6112495238a21',
			'label' => 'Vydavateľ / distribútor',
			'name' => 'vydavatel_distributor',
			'aria-label' => '',
			'type' => 'post_object',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'scd_osobnost_dizajnu',
			),
			'taxonomy' => '',
			'allow_null' => 1,
			'multiple' => 1,
			'return_format' => 'object',
			'ui' => 1,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_61124b1a38a25',
			'label' => 'Cena v dobe vydania',
			'name' => 'cena',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
		array(
			'key' => 'field_6112471138a1e',
			'label' => 'ID',
			'name' => 'id',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
		array(
			'key' => 'field_61124b772110c',
			'label' => 'Galéria',
			'name' => 'obrazky',
			'aria-label' => '',
			'type' => 'gallery',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'return_format' => 'id',
			'preview_size' => 'medium',
			'insert' => 'prepend',
			'library' => 'all',
			'min' => '',
			'max' => '',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => '',
			'mime_types' => '',
		),
		array(
			'key' => 'field_61124bf12110d',
			'label' => 'Články a recenzie',
			'name' => 'clanky_a_recenzie',
			'aria-label' => '',
			'type' => 'gallery',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'return_format' => 'array',
			'preview_size' => 'medium',
			'insert' => 'prepend',
			'library' => 'all',
			'min' => '',
			'max' => '',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => '',
			'mime_types' => '',
		),
		array(
			'key' => 'field_61124b3138a26',
			'label' => 'Kópia hry',
			'name' => 'kopia_hry',
			'aria-label' => '',
			'type' => 'gallery',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'return_format' => 'array',
			'preview_size' => 'medium',
			'insert' => 'prepend',
			'library' => 'all',
			'min' => '',
			'max' => '',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => '',
			'mime_types' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'scd_hra',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => array(
		0 => 'discussion',
		1 => 'comments',
		2 => 'format',
		3 => 'featured_image',
		4 => 'tags',
	),
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );

	acf_add_local_field_group( array(
	'key' => 'group_60cb23bf62af2',
	'title' => 'INOLAB',
	'fields' => array(
		array(
			'key' => 'field_60cb23bf78955',
			'label' => 'Rýchle odkazy',
			'name' => 'rychle_odkazy',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'layout' => 'table',
			'pagination' => 0,
			'min' => 0,
			'max' => 0,
			'collapsed' => 'field_60801a43fc88f',
			'button_label' => 'Pridať odkazz',
			'rows_per_page' => 20,
			'sub_fields' => array(
				array(
					'key' => 'field_60cb23bf86cec',
					'label' => 'Odkaz',
					'name' => 'odkaz',
					'aria-label' => '',
					'type' => 'group',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'layout' => 'block',
					'sub_fields' => array(
						array(
							'key' => 'field_60cb23bf8fb05',
							'label' => 'Text odkazu',
							'name' => 'text_odkazu',
							'aria-label' => '',
							'type' => 'text',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'default_value' => '',
							'placeholder' => '',
							'prepend' => '',
							'append' => '',
							'maxlength' => '',
						),
						array(
							'key' => 'field_60cb23bf8fb41',
							'label' => 'Linka odkazu',
							'name' => 'linka_odkazu',
							'aria-label' => '',
							'type' => 'url',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'relevanssi_exclude' => 0,
							'default_value' => '',
							'placeholder' => '',
						),
						array(
							'key' => 'field_669f68a5d5ed4',
							'label' => 'Ciel odkazu',
							'name' => 'target_odkazu',
							'aria-label' => '',
							'type' => 'select',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '',
								'class' => '',
								'id' => '',
							),
							'relevanssi_exclude' => 0,
							'choices' => array(
								'____' => 'v tomto okne/tabe',
								'_blank' => 'do nového okna/tabu',
								'_self' => 'tento frame',
								'_parent' => 'rodičovský frame',
								'_top' => 'nove plné okno',
							),
							'default_value' => false,
							'return_format' => 'value',
							'multiple' => 0,
							'allow_null' => 0,
							'ui' => 0,
							'ajax' => 0,
							'placeholder' => '',
						),
					),
					'parent_repeater' => 'field_60cb23bf78955',
				),
			),
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => '953',
			),
		),
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => '86021',
			),
		),
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => '86021',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => array(
		0 => 'permalink',
		1 => 'discussion',
		2 => 'comments',
		3 => 'slug',
		4 => 'format',
		5 => 'featured_image',
		6 => 'categories',
		7 => 'tags',
	),
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );

	acf_add_local_field_group( array(
	'key' => 'group_60b8a4990316f',
	'title' => 'Knižnica',
	'fields' => array(
		array(
			'key' => 'field_60b8a4f90e855',
			'label' => 'Lokalita',
			'name' => 'lokalita',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'scd_lokalita',
			'field_type' => 'select',
			'allow_null' => 1,
			'add_term' => 0,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'object',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60afa2eb5a8af',
			'label' => 'Rýchle odkazy',
			'name' => 'rychle_odkazy',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'collapsed' => 'field_60801a43fc88f',
			'min' => 0,
			'max' => 0,
			'layout' => 'table',
			'button_label' => 'Pridať odkaz',
			'sub_fields' => array(
				array(
					'key' => 'field_60afa2eb5a8b0',
					'label' => 'Odkaz',
					'name' => 'odkaz',
					'aria-label' => '',
					'type' => 'group',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'layout' => 'block',
					'sub_fields' => array(
						array(
							'key' => 'field_60afa2eb5a8b1',
							'label' => 'Text odkazu',
							'name' => 'text_odkazu',
							'aria-label' => '',
							'type' => 'text',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'default_value' => '',
							'placeholder' => '',
							'prepend' => '',
							'append' => '',
							'maxlength' => '',
						),
						array(
							'key' => 'field_60afa2eb5a8b2',
							'label' => 'Linka odkazu',
							'name' => 'linka_odkazu',
							'aria-label' => '',
							'type' => 'url',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'default_value' => '',
							'placeholder' => '',
						),
						array(
							'key' => 'field_669f87c4cfb24',
							'label' => 'Cieľ odkazu',
							'name' => 'taget_odkazu',
							'aria-label' => '',
							'type' => 'select',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'choices' => array(
								'____' => 'v tomto okne/tabe',
								'_blank' => 'do nového okna/tabu',
								'_self' => 'tento frame',
								'_parent' => 'rodičovský frame',
								'_top' => 'nove plné okno',
							),
							'default_value' => false,
							'return_format' => 'value',
							'multiple' => 0,
							'allow_null' => 0,
							'ui' => 0,
							'ajax' => 0,
							'placeholder' => '',
						),
					),
					'parent_repeater' => 'field_60afa2eb5a8af',
				),
			),
			'rows_per_page' => 20,
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => '950',
			),
		),
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => '86023',
			),
		),
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => '86023',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => array(
		0 => 'permalink',
		1 => 'discussion',
		2 => 'comments',
		3 => 'slug',
		4 => 'format',
		5 => 'categories',
		6 => 'tags',
	),
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );

	acf_add_local_field_group( array(
	'key' => 'group_62d91f8a5644c',
	'title' => 'Kolekcia',
	'fields' => array(
		array(
			'key' => 'field_62da4bb3276f0',
			'label' => 'Foto',
			'name' => 'foto',
			'aria-label' => '',
			'type' => 'image',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'return_format' => 'id',
			'preview_size' => 'medium',
			'library' => 'all',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => '',
			'mime_types' => '',
		),
		array(
			'key' => 'field_62d91f90b3307',
			'label' => 'Diela',
			'name' => 'diela',
			'aria-label' => '',
			'type' => 'relationship',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'scd_dielo_smd',
				1 => 'scd_dielo_ncd',
			),
			'taxonomy' => '',
			'filters' => array(
				0 => 'search',
			),
			'elements' => '',
			'min' => '',
			'max' => '',
			'return_format' => 'object',
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_6374fd4db6843',
			'label' => 'Osobnosti dizajnu',
			'name' => 'vystavujuci_autori_roly',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'collapsed' => '',
			'min' => 0,
			'max' => 0,
			'layout' => 'table',
			'button_label' => 'Pridať riadok',
			'sub_fields' => array(
				array(
					'key' => 'field_6374fd4db6844',
					'label' => 'Osobnosť',
					'name' => 'osobnost',
					'aria-label' => '',
					'type' => 'post_object',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '50',
						'class' => '',
						'id' => '',
					),
					'post_type' => array(
						0 => 'scd_osobnost_dizajnu',
					),
					'taxonomy' => array(
						0 => 'scd_osobnost_typ:osobnosti-dizajnu',
					),
					'allow_null' => 0,
					'multiple' => 0,
					'return_format' => 'object',
					'ui' => 1,
					'bidirectional_target' => array(
					),
					'parent_repeater' => 'field_6374fd4db6843',
				),
				array(
					'key' => 'field_6374fd4db6845',
					'label' => 'Rola',
					'name' => 'rola',
					'aria-label' => '',
					'type' => 'taxonomy',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '50',
						'class' => '',
						'id' => '',
					),
					'taxonomy' => 'scd_rola',
					'field_type' => 'select',
					'allow_null' => 0,
					'add_term' => 1,
					'save_terms' => 0,
					'load_terms' => 0,
					'return_format' => 'object',
					'multiple' => 0,
					'bidirectional_target' => array(
					),
					'parent_repeater' => 'field_6374fd4db6843',
				),
			),
			'rows_per_page' => 20,
		),
		array(
			'key' => 'field_6374fd5eb6846',
			'label' => 'Výrobcovia / firmy / školy',
			'name' => 'vyrobcovia_firmy_skoly',
			'aria-label' => '',
			'type' => 'relationship',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'scd_osobnost_dizajnu',
			),
			'taxonomy' => array(
				0 => 'scd_osobnost_typ:vyrobcovia',
			),
			'filters' => array(
				0 => 'search',
			),
			'elements' => '',
			'min' => '',
			'max' => '',
			'return_format' => 'object',
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_6374fd70b6847',
			'label' => 'Súvisiace články',
			'name' => 'suvisiace_clanky',
			'aria-label' => '',
			'type' => 'relationship',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'post',
			),
			'taxonomy' => '',
			'filters' => array(
				0 => 'search',
			),
			'elements' => '',
			'min' => '',
			'max' => '',
			'return_format' => 'object',
			'bidirectional_target' => array(
			),
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'taxonomy',
				'operator' => '==',
				'value' => 'scd_kolekcia',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );

	acf_add_local_field_group( array(
	'key' => 'group_607402b535c53',
	'title' => 'Lokalita',
	'fields' => array(
		array(
			'key' => 'field_607402d93c437',
			'label' => 'Adresa',
			'name' => 'adresa',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
		array(
			'key' => 'field_607407783c438',
			'label' => 'GPS',
			'name' => 'gps',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
		array(
			'key' => 'field_60740a1f3c439',
			'label' => 'Otváracie hodiny',
			'name' => 'otvaracie_hodiny',
			'aria-label' => '',
			'type' => 'textarea',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'maxlength' => '',
			'rows' => '',
			'new_lines' => '',
		),
		array(
			'key' => 'field_60759d2113143',
			'label' => 'Vstupné',
			'name' => 'vstupne',
			'aria-label' => '',
			'type' => 'number',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '€',
			'min' => '',
			'max' => '',
			'step' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'taxonomy',
				'operator' => '==',
				'value' => 'scd_lokalita',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );

	acf_add_local_field_group( array(
	'key' => 'group_608016d15698f',
	'title' => 'Národná cena za dizajn',
	'fields' => array(
		array(
			'key' => 'field_608016d59403f',
			'label' => 'Rýchle odkazy',
			'name' => 'rychle_odkazy',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'collapsed' => 'field_60801a43fc88f',
			'min' => 0,
			'max' => 0,
			'layout' => 'table',
			'button_label' => 'Pridať odkaz',
			'sub_fields' => array(
				array(
					'key' => 'field_60801a43fc88f',
					'label' => 'Odkaz',
					'name' => 'odkaz',
					'aria-label' => '',
					'type' => 'group',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'layout' => 'block',
					'sub_fields' => array(
						array(
							'key' => 'field_60801a77fc890',
							'label' => 'Text odkazu',
							'name' => 'text_odkazu',
							'aria-label' => '',
							'type' => 'text',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'default_value' => '',
							'placeholder' => '',
							'prepend' => '',
							'append' => '',
							'maxlength' => '',
						),
						array(
							'key' => 'field_60801aa1fc891',
							'label' => 'Linka odkazu',
							'name' => 'linka_odkazu',
							'aria-label' => '',
							'type' => 'url',
							'instructions' => '',
							'required' => false,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'relevanssi_exclude' => 0,
							'default_value' => '',
							'placeholder' => '',
						),
						array(
							'key' => 'field_669f6d4e76d64',
							'label' => 'Ciel odkazu',
							'name' => 'target_odkazu',
							'aria-label' => '',
							'type' => 'select',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'choices' => array(
								'____' => 'v tomto okne/tabe',
								'_blank' => 'do nového okna/tabu',
								'_self' => 'tento frame',
								'_parent' => 'rodičovský frame',
								'_top' => 'nove plné okno',
							),
							'default_value' => false,
							'return_format' => 'value',
							'multiple' => 0,
							'allow_null' => 0,
							'ui' => 0,
							'ajax' => 0,
							'placeholder' => '',
						),
					),
					'parent_repeater' => 'field_608016d59403f',
				),
			),
			'rows_per_page' => 20,
		),
		array(
			'key' => 'field_608023541fa5c',
			'label' => 'Ako sa zúčastniť',
			'name' => 'ako_sa_zucastnit',
			'aria-label' => '',
			'type' => 'textarea',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'maxlength' => '',
			'rows' => '',
			'new_lines' => 'wpautop',
		),
		array(
			'key' => 'field_608024011fa5d',
			'label' => 'Registrovať sa',
			'name' => 'registrovat_sa',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
		),
		array(
			'key' => 'field_620e1caa6d542',
			'label' => 'Články',
			'name' => 'clanky',
			'aria-label' => '',
			'type' => 'relationship',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'post',
			),
			'taxonomy' => '',
			'filters' => array(
				0 => 'search',
			),
			'elements' => '',
			'min' => '',
			'max' => '',
			'return_format' => 'object',
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60812eb5e519f',
			'label' => 'Porota',
			'name' => 'porota',
			'aria-label' => '',
			'type' => 'relationship',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'scd_autor',
			),
			'taxonomy' => '',
			'filters' => array(
				0 => 'search',
			),
			'elements' => '',
			'min' => '',
			'max' => '',
			'return_format' => 'object',
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60d34d6e37fdb',
			'label' => 'Videá',
			'name' => 'videa',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'collapsed' => 'field_60d34d6e37fdc',
			'min' => 2,
			'max' => 2,
			'layout' => 'table',
			'button_label' => 'Pridať video',
			'sub_fields' => array(
				array(
					'key' => 'field_60d34d6e37fdc',
					'label' => 'Video',
					'name' => 'video',
					'aria-label' => '',
					'type' => 'group',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'layout' => 'block',
					'sub_fields' => array(
						array(
							'key' => 'field_60d34d6e37fdd',
							'label' => 'Názov videa',
							'name' => 'nazov_videa',
							'aria-label' => '',
							'type' => 'text',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'default_value' => '',
							'placeholder' => '',
							'prepend' => '',
							'append' => '',
							'maxlength' => '',
						),
						array(
							'key' => 'field_60d34d6e37fde',
							'label' => 'Youtube linka',
							'name' => 'youtube_linka',
							'aria-label' => '',
							'type' => 'url',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'default_value' => '',
							'placeholder' => '',
						),
					),
					'parent_repeater' => 'field_60d34d6e37fdb',
				),
			),
			'rows_per_page' => 20,
		),
		array(
			'key' => 'field_60d3507d3a54c',
			'label' => 'Youtube kanál',
			'name' => 'youtube_kanal',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
		),
		array(
			'key' => 'field_60d36d2599480',
			'label' => 'Práce',
			'name' => 'prace',
			'aria-label' => '',
			'type' => 'relationship',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'scd_dielo_ncd',
			),
			'taxonomy' => '',
			'filters' => array(
				0 => 'search',
			),
			'elements' => '',
			'min' => '',
			'max' => '',
			'return_format' => 'object',
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_620e248ba63e4',
			'label' => 'Podujatia',
			'name' => 'podujatia',
			'aria-label' => '',
			'type' => 'relationship',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'scd_vystava',
				1 => 'scd_podujatie',
			),
			'taxonomy' => '',
			'filters' => array(
				0 => 'search',
			),
			'elements' => '',
			'min' => '',
			'max' => '',
			'return_format' => 'object',
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_62d7dd12221d2',
			'label' => 'Práce URL',
			'name' => 'prace_url',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
		),
		array(
			'key' => 'field_6080244c1fa5e',
			'label' => 'Galéria',
			'name' => 'galeria',
			'aria-label' => '',
			'type' => 'gallery',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'return_format' => 'array',
			'preview_size' => 'thumbnail',
			'insert' => 'prepend',
			'library' => 'all',
			'min' => '',
			'max' => '',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => '',
			'mime_types' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'scd_ncd',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => array(
		0 => 'excerpt',
		1 => 'discussion',
		2 => 'comments',
		3 => 'slug',
		4 => 'format',
		5 => 'categories',
		6 => 'tags',
		7 => 'send-trackbacks',
	),
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );

	acf_add_local_field_group( array(
	'key' => 'group_60a3e14185d92',
	'title' => 'Osobnosť dizajnu',
	'fields' => array(
		array(
			'key' => 'field_60af5cfdfc169',
			'label' => 'Typ',
			'name' => 'typ',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'scd_osobnost_typ',
			'field_type' => 'radio',
			'allow_null' => 0,
			'add_term' => 1,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'object',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60a3e1b0a6fc7',
			'label' => 'Značky',
			'name' => 'znacky',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'post_tag',
			'field_type' => 'multi_select',
			'allow_null' => 1,
			'add_term' => 1,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'object',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60a3e224a6fc8',
			'label' => 'Zameranie',
			'name' => 'zameranie',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'scd_zameranie',
			'field_type' => 'multi_select',
			'allow_null' => 1,
			'add_term' => 1,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'object',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60a3e38ca6fc9',
			'label' => 'Dátum narodenia / založenia',
			'name' => 'datum_zaciatku',
			'aria-label' => '',
			'type' => 'number',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'min' => '',
			'max' => '',
			'step' => '',
		),
		array(
			'key' => 'field_60a3e3caa6fca',
			'label' => 'Dátum úmrtia / ukončenia',
			'name' => 'datum_konca',
			'aria-label' => '',
			'type' => 'number',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'min' => '',
			'max' => '',
			'step' => '',
		),
		array(
			'key' => 'field_60a3e4b9a6fcd',
			'label' => 'Články',
			'name' => 'clanky',
			'aria-label' => '',
			'type' => 'relationship',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'post',
			),
			'taxonomy' => '',
			'filters' => array(
				0 => 'search',
			),
			'elements' => '',
			'min' => '',
			'max' => '',
			'return_format' => 'id',
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60a3e58ca6fcf',
			'label' => 'Galéria',
			'name' => 'galeria',
			'aria-label' => '',
			'type' => 'gallery',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'return_format' => 'array',
			'preview_size' => 'medium',
			'insert' => 'prepend',
			'library' => 'all',
			'min' => '',
			'max' => '',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => '',
			'mime_types' => '',
		),
		array(
			'key' => 'field_60e870b360a4a',
			'label' => 'ID',
			'name' => 'id',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'scd_osobnost_dizajnu',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => array(
		0 => 'discussion',
		1 => 'comments',
		2 => 'format',
		3 => 'tags',
		4 => 'send-trackbacks',
	),
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );

	acf_add_local_field_group( array(
	'key' => 'group_60755ba68af14',
	'title' => 'Podujatie',
	'fields' => array(
		array(
			'key' => 'field_60755cab2f3fb',
			'label' => 'Lokalita',
			'name' => 'lokalita',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '25',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'scd_lokalita',
			'field_type' => 'select',
			'allow_null' => 1,
			'add_term' => 1,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'object',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_61658e06cf7f9',
			'label' => 'Lokalita / text',
			'name' => 'lokalita_text',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '25',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
		array(
			'key' => 'field_635aa3c51aeaf',
			'label' => 'Miesto',
			'name' => 'miesto',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '25',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'scd_miesto',
			'field_type' => 'multi_select',
			'allow_null' => 0,
			'add_term' => 0,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'object',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_6076a2f628022',
			'label' => 'Značky',
			'name' => 'znacky',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '25',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'post_tag',
			'field_type' => 'multi_select',
			'allow_null' => 1,
			'add_term' => 1,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'object',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_6076a15d28020',
			'label' => 'Typ podujatia',
			'name' => 'typ_podujatia',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '25',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'scd_podujatie_typ',
			'field_type' => 'radio',
			'allow_null' => 1,
			'add_term' => 0,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'object',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60755bb18574c',
			'label' => 'Trvanie od',
			'name' => 'trvanie_od',
			'aria-label' => '',
			'type' => 'date_picker',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '25',
				'class' => '',
				'id' => '',
			),
			'display_format' => 'j. F Y',
			'return_format' => 'j. F Y',
			'first_day' => 1,
		),
		array(
			'key' => 'field_60755bda8574d',
			'label' => 'Trvanie do',
			'name' => 'trvanie_do',
			'aria-label' => '',
			'type' => 'date_picker',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '25',
				'class' => '',
				'id' => '',
			),
			'display_format' => 'j. F Y',
			'return_format' => 'j. F Y',
			'first_day' => 1,
		),
		array(
			'key' => 'field_6076a19628021',
			'label' => 'Vstupné',
			'name' => 'vstupne',
			'aria-label' => '',
			'type' => 'number',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '25',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '€',
			'min' => '',
			'max' => '',
			'step' => '',
		),
		array(
			'key' => 'field_60881e032ed67',
			'label' => 'Galéria',
			'name' => 'galeria',
			'aria-label' => '',
			'type' => 'gallery',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'return_format' => 'array',
			'preview_size' => 'full',
			'insert' => 'prepend',
			'library' => 'all',
			'min' => '',
			'max' => '',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => '',
			'mime_types' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'scd_podujatie',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => array(
		0 => 'discussion',
		1 => 'comments',
		2 => 'format',
		3 => 'tags',
	),
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );

	acf_add_local_field_group( array(
	'key' => 'group_609bd87d241b4',
	'title' => 'Publikácia',
	'fields' => array(
		array(
			'key' => 'field_609bd8b601e91',
			'label' => 'Autor',
			'name' => 'autori',
			'aria-label' => '',
			'type' => 'relationship',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'scd_autor',
				1 => 'scd_osobnost_dizajnu',
			),
			'taxonomy' => '',
			'filters' => array(
				0 => 'search',
			),
			'elements' => '',
			'min' => '',
			'max' => '',
			'return_format' => 'object',
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_609bd9f52beaf',
			'label' => 'Dátum vydania',
			'name' => 'datum_vydania',
			'aria-label' => '',
			'type' => 'number',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'min' => '',
			'max' => '',
			'step' => '',
		),
		array(
			'key' => 'field_609bda4d2beb0',
			'label' => 'Vydavateľstvo',
			'name' => 'vydavatelstvo',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'scd_vydavatelstvo',
			'field_type' => 'select',
			'allow_null' => 1,
			'add_term' => 1,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'object',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_609bda732beb1',
			'label' => 'Značky',
			'name' => 'znacky',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'post_tag',
			'field_type' => 'multi_select',
			'allow_null' => 1,
			'add_term' => 1,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'object',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_609bdcac2beb2',
			'label' => 'Anotácia',
			'name' => 'anotacia',
			'aria-label' => '',
			'type' => 'textarea',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'maxlength' => '',
			'rows' => '',
			'new_lines' => '',
		),
		array(
			'key' => 'field_609be08d2beb3',
			'label' => 'Dizajnéri v publikácii',
			'name' => 'dizajneri_v_publikacii',
			'aria-label' => '',
			'type' => 'relationship',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'scd_osobnost_dizajnu',
			),
			'taxonomy' => '',
			'filters' => array(
				0 => 'search',
			),
			'elements' => '',
			'min' => '',
			'max' => '',
			'return_format' => 'object',
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60ed82e8c5023',
			'label' => 'Cena',
			'name' => 'cena',
			'aria-label' => '',
			'type' => 'number',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '€',
			'min' => '',
			'max' => '',
			'step' => '',
		),
		array(
			'key' => 'field_60f581ee7774b',
			'label' => 'ID',
			'name' => 'id',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'scd_publikacia',
			),
		),
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'scd_pub_kniznice',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => array(
		0 => 'discussion',
		1 => 'comments',
		2 => 'format',
		3 => 'tags',
	),
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );

	acf_add_local_field_group( array(
	'key' => 'group_60cb67b5044d7',
	'title' => 'Publikácie',
	'fields' => array(
		array(
			'key' => 'field_60cb67b513136',
			'label' => 'Lokalita',
			'name' => 'lokalita',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'scd_lokalita',
			'field_type' => 'select',
			'allow_null' => 1,
			'add_term' => 0,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'object',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60cb67b51315c',
			'label' => 'Rýchle odkazy',
			'name' => 'rychle_odkazy',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'collapsed' => 'field_60801a43fc88f',
			'min' => 0,
			'max' => 0,
			'layout' => 'table',
			'button_label' => 'Pridať odkaz',
			'sub_fields' => array(
				array(
					'key' => 'field_60cb67b52322d',
					'label' => 'Odkaz',
					'name' => 'odkaz',
					'aria-label' => '',
					'type' => 'group',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'layout' => 'block',
					'sub_fields' => array(
						array(
							'key' => 'field_60cb67b527bb1',
							'label' => 'Text odkazu',
							'name' => 'text_odkazu',
							'aria-label' => '',
							'type' => 'text',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'default_value' => '',
							'placeholder' => '',
							'prepend' => '',
							'append' => '',
							'maxlength' => '',
						),
						array(
							'key' => 'field_60cb67b527c20',
							'label' => 'Linka odkazu',
							'name' => 'linka_odkazu',
							'aria-label' => '',
							'type' => 'url',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'default_value' => '',
							'placeholder' => '',
						),
						array(
							'key' => 'field_669f884db81b9',
							'label' => 'Cieľ odkazu',
							'name' => 'target_odkazu',
							'aria-label' => '',
							'type' => 'select',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'choices' => array(
								'____' => 'v tomto okne/tabe',
								'_blank' => 'do nového okna/tabu',
								'_self' => 'tento frame',
								'_parent' => 'rodičovský frame',
								'_top' => 'nove plné okno',
							),
							'default_value' => false,
							'return_format' => 'value',
							'multiple' => 0,
							'allow_null' => 0,
							'ui' => 0,
							'ajax' => 0,
							'placeholder' => '',
						),
					),
					'parent_repeater' => 'field_60cb67b51315c',
				),
			),
			'rows_per_page' => 20,
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => '306133',
			),
		),
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => '106352',
			),
		),
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => '106352',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => array(
		0 => 'permalink',
		1 => 'discussion',
		2 => 'comments',
		3 => 'slug',
		4 => 'format',
		5 => 'categories',
		6 => 'tags',
	),
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );

	acf_add_local_field_group( array(
	'key' => 'group_60d350caccc86',
	'title' => 'Rok slovenského dizajnu',
	'fields' => array(
		array(
			'key' => 'field_60d350b6ba7ce',
			'label' => 'Videá',
			'name' => 'videa',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'collapsed' => 'field_60d34d6e37fdc',
			'min' => 2,
			'max' => 2,
			'layout' => 'table',
			'button_label' => 'Pridať video',
			'sub_fields' => array(
				array(
					'key' => 'field_60d350b6ba7cf',
					'label' => 'Video',
					'name' => 'video',
					'aria-label' => '',
					'type' => 'group',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'layout' => 'block',
					'sub_fields' => array(
						array(
							'key' => 'field_60d350b6ba7d0',
							'label' => 'Názov videa',
							'name' => 'nazov_videa',
							'aria-label' => '',
							'type' => 'text',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'default_value' => '',
							'placeholder' => '',
							'prepend' => '',
							'append' => '',
							'maxlength' => '',
						),
						array(
							'key' => 'field_60d350b6ba7d1',
							'label' => 'Youtube linka',
							'name' => 'youtube_linka',
							'aria-label' => '',
							'type' => 'url',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'default_value' => '',
							'placeholder' => '',
						),
					),
					'parent_repeater' => 'field_60d350b6ba7ce',
				),
			),
			'rows_per_page' => 20,
		),
		array(
			'key' => 'field_60d350bdba7d2',
			'label' => 'Youtube kanál',
			'name' => 'youtube_kanal',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => '246',
			),
		),
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => '86031',
			),
		),
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => '86031',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => array(
		0 => 'permalink',
		1 => 'discussion',
		2 => 'comments',
		3 => 'slug',
		4 => 'format',
		5 => 'tags',
	),
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );

	acf_add_local_field_group( array(
	'key' => 'group_60caf6bb99bb7',
	'title' => 'Slovenské múzeum dizajnu',
	'fields' => array(
		array(
			'key' => 'field_60caf6bba2e27',
			'label' => 'Lokalita',
			'name' => 'lokalita',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'scd_lokalita',
			'field_type' => 'select',
			'allow_null' => 1,
			'add_term' => 0,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'object',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60caf6bba2e64',
			'label' => 'Rýchle odkazy',
			'name' => 'rychle_odkazy',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'collapsed' => 'field_60801a43fc88f',
			'min' => 0,
			'max' => 0,
			'layout' => 'table',
			'button_label' => 'Pridať odkaz',
			'sub_fields' => array(
				array(
					'key' => 'field_60caf6bbabc03',
					'label' => 'Odkaz',
					'name' => 'odkaz',
					'aria-label' => '',
					'type' => 'group',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'layout' => 'block',
					'sub_fields' => array(
						array(
							'key' => 'field_60caf6bbae9e4',
							'label' => 'Text odkazu',
							'name' => 'text_odkazu',
							'aria-label' => '',
							'type' => 'text',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'default_value' => '',
							'placeholder' => '',
							'prepend' => '',
							'append' => '',
							'maxlength' => '',
						),
						array(
							'key' => 'field_60caf6bbaea20',
							'label' => 'Linka odkazu',
							'name' => 'linka_odkazu',
							'aria-label' => '',
							'type' => 'url',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'default_value' => '',
							'placeholder' => '',
						),
						array(
							'key' => 'field_669f892bce612',
							'label' => 'Cieľ odkazu',
							'name' => 'target_odkazu',
							'aria-label' => '',
							'type' => 'select',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'choices' => array(
								'____' => 'v tomto okne/tabe',
								'_blank' => 'do nového okna/tabu',
								'_self' => 'tento frame',
								'_parent' => 'rodičovský frame',
								'_top' => 'nove plné okno',
							),
							'default_value' => false,
							'return_format' => 'value',
							'multiple' => 0,
							'allow_null' => 0,
							'ui' => 0,
							'ajax' => 0,
							'placeholder' => '',
						),
					),
					'parent_repeater' => 'field_60caf6bba2e64',
				),
			),
			'rows_per_page' => 20,
		),
		array(
			'key' => 'field_624fd9ed15f47',
			'label' => 'Videá',
			'name' => 'videa',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'collapsed' => 'field_60d34d6e37fdc',
			'min' => 2,
			'max' => 2,
			'layout' => 'table',
			'button_label' => 'Pridať video',
			'sub_fields' => array(
				array(
					'key' => 'field_624fd9ed15f48',
					'label' => 'Video',
					'name' => 'video',
					'aria-label' => '',
					'type' => 'group',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'layout' => 'block',
					'sub_fields' => array(
						array(
							'key' => 'field_624fd9ed15f49',
							'label' => 'Názov videa',
							'name' => 'nazov_videa',
							'aria-label' => '',
							'type' => 'text',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'default_value' => '',
							'placeholder' => '',
							'prepend' => '',
							'append' => '',
							'maxlength' => '',
						),
						array(
							'key' => 'field_624fd9ed15f4a',
							'label' => 'Youtube linka',
							'name' => 'youtube_linka',
							'aria-label' => '',
							'type' => 'url',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'default_value' => '',
							'placeholder' => '',
						),
					),
					'parent_repeater' => 'field_624fd9ed15f47',
				),
			),
			'rows_per_page' => 20,
		),
		array(
			'key' => 'field_624fd9dd15f46',
			'label' => 'Youtube kanál',
			'name' => 'youtube_kanal',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => '952',
			),
		),
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => '86029',
			),
		),
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => '86029',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => array(
		0 => 'permalink',
		1 => 'discussion',
		2 => 'comments',
		3 => 'slug',
		4 => 'format',
		5 => 'categories',
		6 => 'tags',
	),
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );

	acf_add_local_field_group( array(
	'key' => 'group_61f1248f869f9',
	'title' => 'Štatistický zber dát',
	'fields' => array(
		array(
			'key' => 'field_61f1248feaaae',
			'label' => 'Rýchle odkazy',
			'name' => 'rychle_odkazy',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'layout' => 'table',
			'pagination' => 0,
			'min' => 0,
			'max' => 0,
			'collapsed' => 'field_60801a43fc88f',
			'button_label' => 'Pridať odkazzz',
			'rows_per_page' => 20,
			'sub_fields' => array(
				array(
					'key' => 'field_61f1249004d00',
					'label' => 'Odkaz',
					'name' => 'odkaz',
					'aria-label' => '',
					'type' => 'group',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'relevanssi_exclude' => 0,
					'layout' => 'table',
					'sub_fields' => array(
						array(
							'key' => 'field_61f1249015520',
							'label' => 'Text odkazu',
							'name' => 'text_odkazu',
							'aria-label' => '',
							'type' => 'text',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'default_value' => '',
							'placeholder' => '',
							'prepend' => '',
							'append' => '',
							'maxlength' => '',
						),
						array(
							'key' => 'field_61f124901555f',
							'label' => 'Linka odkazu',
							'name' => 'linka_odkazu',
							'aria-label' => '',
							'type' => 'url',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'default_value' => '',
							'placeholder' => '',
						),
						array(
							'key' => 'field_669f72cb3d2d1',
							'label' => 'Cieľ odkazu',
							'name' => 'target_odkazu',
							'aria-label' => '',
							'type' => 'select',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '50',
								'class' => '',
								'id' => '',
							),
							'choices' => array(
								'____' => 'v tomto okne/tabe',
								'_blank' => 'do nového okna/tabu',
								'_self' => 'tento frame',
								'_parent' => 'rodičovský frame',
								'_top' => 'nove plné okno',
							),
							'default_value' => false,
							'return_format' => 'value',
							'multiple' => 0,
							'allow_null' => 0,
							'ui' => 0,
							'ajax' => 0,
							'placeholder' => '',
						),
					),
					'parent_repeater' => 'field_61f1248feaaae',
				),
			),
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => '874940',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => array(
		0 => 'permalink',
		1 => 'discussion',
		2 => 'comments',
		3 => 'slug',
		4 => 'format',
		5 => 'categories',
		6 => 'tags',
	),
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );

	acf_add_local_field_group( array(
	'key' => 'group_607410f5d6d1e',
	'title' => 'Výstava',
	'fields' => array(
		array(
			'key' => 'field_60740ca130798',
			'label' => 'Lokalita',
			'name' => 'lokalita',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '25',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'scd_lokalita',
			'field_type' => 'select',
			'allow_null' => 0,
			'add_term' => 1,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'object',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_6101275973354',
			'label' => 'Lokalita / text',
			'name' => 'lokalita_text',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '25',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
		array(
			'key' => 'field_635aa2d0f5e52',
			'label' => 'Miesto',
			'name' => 'miesto',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '25',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'scd_miesto',
			'field_type' => 'multi_select',
			'allow_null' => 0,
			'add_term' => 0,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'object',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60759ddae542c',
			'label' => 'Vstupné',
			'name' => 'vstupne',
			'aria-label' => '',
			'type' => 'number',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '25',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '€',
			'min' => '',
			'max' => '',
			'step' => '',
		),
		array(
			'key' => 'field_607411ed4bbf7',
			'label' => 'Trvanie od',
			'name' => 'trvanie_od',
			'aria-label' => '',
			'type' => 'date_picker',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '25',
				'class' => '',
				'id' => '',
			),
			'display_format' => 'j. F Y',
			'return_format' => 'j. F Y',
			'first_day' => 1,
		),
		array(
			'key' => 'field_6074129e4bbf8',
			'label' => 'Trvanie do',
			'name' => 'trvanie_do',
			'aria-label' => '',
			'type' => 'date_picker',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '25',
				'class' => '',
				'id' => '',
			),
			'display_format' => 'j. F Y',
			'return_format' => 'j. F Y',
			'first_day' => 1,
		),
		array(
			'key' => 'field_6076a1da4d894',
			'label' => 'Značky',
			'name' => 'znacky',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'post_tag',
			'field_type' => 'multi_select',
			'allow_null' => 1,
			'add_term' => 1,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'object',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_607412e24bbf9',
			'label' => 'Kurátor / autor',
			'name' => 'kurator_autor',
			'aria-label' => '',
			'type' => 'post_object',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'scd_autor',
				1 => 'scd_osobnost_dizajnu',
			),
			'taxonomy' => '',
			'allow_null' => 1,
			'multiple' => 1,
			'return_format' => 'object',
			'ui' => 1,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_607414684bbfa',
			'label' => 'Kurátor / text',
			'name' => 'kurator_text',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
		array(
			'key' => 'field_62d9060792284',
			'label' => 'Sekcie výstavy (kolekcia)',
			'name' => 'kolekcia',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'taxonomy' => 'scd_kolekcia',
			'field_type' => 'select',
			'allow_null' => 1,
			'add_term' => 1,
			'save_terms' => 1,
			'load_terms' => 1,
			'return_format' => 'id',
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_61016b00071aa',
			'label' => 'Osobnosti dizajnu na výstave',
			'name' => 'vystavujuci_autori_roly',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'collapsed' => '',
			'min' => 0,
			'max' => 0,
			'layout' => 'table',
			'button_label' => 'Pridať riadok',
			'sub_fields' => array(
				array(
					'key' => 'field_61016b00071ab',
					'label' => 'Osobnosť',
					'name' => 'osobnost',
					'aria-label' => '',
					'type' => 'post_object',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '50',
						'class' => '',
						'id' => '',
					),
					'post_type' => array(
						0 => 'scd_osobnost_dizajnu',
					),
					'taxonomy' => array(
						0 => 'scd_osobnost_typ:osobnosti-dizajnu',
					),
					'allow_null' => 0,
					'multiple' => 0,
					'return_format' => 'object',
					'ui' => 1,
					'bidirectional_target' => array(
					),
					'parent_repeater' => 'field_61016b00071aa',
				),
				array(
					'key' => 'field_61016b00071ac',
					'label' => 'Rola',
					'name' => 'rola',
					'aria-label' => '',
					'type' => 'taxonomy',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '50',
						'class' => '',
						'id' => '',
					),
					'taxonomy' => 'scd_rola',
					'field_type' => 'select',
					'allow_null' => 0,
					'add_term' => 1,
					'save_terms' => 0,
					'load_terms' => 0,
					'return_format' => 'object',
					'multiple' => 0,
					'bidirectional_target' => array(
					),
					'parent_repeater' => 'field_61016b00071aa',
				),
			),
			'rows_per_page' => 20,
		),
		array(
			'key' => 'field_62d901fce9b29',
			'label' => 'Výrobcovia / firmy / školy',
			'name' => 'vyrobcovia_firmy_skoly',
			'aria-label' => '',
			'type' => 'relationship',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'scd_osobnost_dizajnu',
			),
			'taxonomy' => array(
				0 => 'scd_osobnost_typ:vyrobcovia',
			),
			'filters' => array(
				0 => 'search',
			),
			'elements' => '',
			'min' => '',
			'max' => '',
			'return_format' => 'object',
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60743f1ccbccf',
			'label' => 'Súvisiace podujatia',
			'name' => 'suvisiace_podujatia',
			'aria-label' => '',
			'type' => 'relationship',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'scd_podujatie',
			),
			'taxonomy' => '',
			'filters' => array(
				0 => 'search',
			),
			'elements' => '',
			'min' => '',
			'max' => '',
			'return_format' => 'object',
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_62d904a63f6c1',
			'label' => 'Články k výstave',
			'name' => 'clanky_k_vystave',
			'aria-label' => '',
			'type' => 'relationship',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'post',
			),
			'taxonomy' => '',
			'filters' => array(
				0 => 'search',
			),
			'elements' => '',
			'min' => '',
			'max' => '',
			'return_format' => 'object',
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_607414fc4bbfb',
			'label' => 'Galéria',
			'name' => 'galeria',
			'aria-label' => '',
			'type' => 'gallery',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'return_format' => 'array',
			'preview_size' => 'full',
			'insert' => 'append',
			'library' => 'all',
			'min' => '',
			'max' => '',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => '',
			'mime_types' => '',
		),
		array(
			'key' => 'field_607421fe6ed42',
			'label' => 'Prílohy',
			'name' => 'prilohy',
			'aria-label' => '',
			'type' => 'gallery',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'return_format' => 'array',
			'preview_size' => 'full',
			'insert' => 'append',
			'library' => 'all',
			'min' => '',
			'max' => '',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => '',
			'mime_types' => 'pdf,doc,docx,odt,xls,xlsx,ods,jpeg,jpg,png,gif,ai,zip',
		),
		array(
			'key' => 'field_60fec6c809478',
			'label' => 'ID',
			'name' => 'id',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '25',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'scd_vystava',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => array(
		0 => 'discussion',
		1 => 'comments',
		2 => 'format',
		3 => 'tags',
	),
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );

	acf_add_local_field_group( array(
	'key' => 'group_607d2e663a116',
	'title' => 'Mini banner',
	'fields' => array(
		array(
			'key' => 'field_60801ec3b7a42',
			'label' => 'Text',
			'name' => 'mini_banner_text',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
		array(
			'key' => 'field_60801ee5b7a43',
			'label' => 'Obrázok',
			'name' => 'mini_banner_obrazok',
			'aria-label' => '',
			'type' => 'image',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'return_format' => 'url',
			'preview_size' => 'thumbnail',
			'library' => 'all',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => '',
			'mime_types' => '',
		),
		array(
			'key' => 'field_60801f54b7a44',
			'label' => 'Nadpis',
			'name' => 'mini_banner_nadpis',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
		array(
			'key' => 'field_60801f7fb7a45',
			'label' => 'Linka',
			'name' => 'mini_banner_linka',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
		),
		array(
			'key' => 'field_60801fa1b7a46',
			'label' => 'Popis',
			'name' => 'mini_banner_popis',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => 86004,
			),
		),
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => '86012',
			),
		),
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => 86012,
			),
		),
	),
	'menu_order' => 1,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => array(
		0 => 'permalink',
		1 => 'the_content',
		2 => 'excerpt',
		3 => 'discussion',
		4 => 'comments',
		5 => 'slug',
		6 => 'author',
		7 => 'format',
		8 => 'page_attributes',
		9 => 'featured_image',
		10 => 'categories',
		11 => 'tags',
		12 => 'send-trackbacks',
	),
	'active' => true,
	'description' => '',
	'show_in_rest' => false,
) );

	acf_add_local_field_group( array(
	'key' => 'group_629080482047f',
	'title' => 'Hero sekcia',
	'fields' => array(
		array(
			'key' => 'field_629081ae2b37c',
			'label' => 'Vľavo',
			'name' => 'hero_vlavo',
			'aria-label' => '',
			'type' => 'post_object',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'post',
				1 => 'page',
				2 => 'scd_publikacia',
				3 => 'scd_pub_kniznice',
			),
			'taxonomy' => '',
			'allow_null' => 0,
			'multiple' => 0,
			'return_format' => 'object',
			'ui' => 1,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_629082af2b37d',
			'label' => 'Vpravo hore',
			'name' => 'hero_vpravo_hore',
			'aria-label' => '',
			'type' => 'post_object',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'post',
				1 => 'page',
				2 => 'scd_publikacia',
				3 => 'scd_pub_kniznice',
			),
			'taxonomy' => '',
			'allow_null' => 0,
			'multiple' => 0,
			'return_format' => 'object',
			'ui' => 1,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_629082eb2b37e',
			'label' => 'Vpravo dole',
			'name' => 'hero_vpravo_dole',
			'aria-label' => '',
			'type' => 'post_object',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'post',
				1 => 'page',
				2 => 'scd_publikacia',
				3 => 'scd_pub_kniznice',
			),
			'taxonomy' => '',
			'allow_null' => 0,
			'multiple' => 0,
			'return_format' => 'object',
			'ui' => 1,
			'bidirectional_target' => array(
			),
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => '86004',
			),
		),
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => '86012',
			),
		),
	),
	'menu_order' => 2,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => array(
		0 => 'permalink',
		1 => 'the_content',
		2 => 'excerpt',
		3 => 'discussion',
		4 => 'comments',
		5 => 'slug',
		6 => 'author',
		7 => 'format',
		8 => 'page_attributes',
		9 => 'featured_image',
		10 => 'categories',
		11 => 'tags',
		12 => 'send-trackbacks',
	),
	'active' => true,
	'description' => '',
	'show_in_rest' => false,
) );

	acf_add_local_field_group( array(
	'key' => 'group_60d36fc36372d',
	'title' => 'Dielo dizajnu',
	'fields' => array(
		array(
			'key' => 'field_60d37000b859e',
			'label' => 'Nadpis',
			'name' => 'dielo_mesiaca_nadpis',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
		array(
			'key' => 'field_60d37072b85a1',
			'label' => 'Dielo',
			'name' => 'dielo_mesiaca_dielo',
			'aria-label' => '',
			'type' => 'post_object',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'post_type' => array(
				0 => 'scd_dielo_smd',
				1 => 'scd_dielo_ncd',
			),
			'taxonomy' => '',
			'allow_null' => 1,
			'multiple' => 0,
			'return_format' => 'object',
			'ui' => 1,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_60d37017b859f',
			'label' => 'Text',
			'name' => 'dielo_mesiaca_text',
			'aria-label' => '',
			'type' => 'textarea',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'maxlength' => '',
			'rows' => '',
			'new_lines' => '',
		),
		array(
			'key' => 'field_60d37167b85a2',
			'label' => 'Galéria',
			'name' => 'dielo_mesiaca_galeria',
			'aria-label' => '',
			'type' => 'gallery',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'return_format' => 'array',
			'preview_size' => 'medium',
			'insert' => 'prepend',
			'library' => 'all',
			'min' => '',
			'max' => '',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => '',
			'mime_types' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => 86004,
			),
		),
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => '86012',
			),
		),
		array(
			array(
				'param' => 'page',
				'operator' => '==',
				'value' => 86012,
			),
		),
	),
	'menu_order' => 3,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => array(
		0 => 'permalink',
		1 => 'the_content',
		2 => 'excerpt',
		3 => 'discussion',
		4 => 'comments',
		5 => 'slug',
		6 => 'author',
		7 => 'format',
		8 => 'page_attributes',
		9 => 'featured_image',
		10 => 'categories',
		11 => 'tags',
		12 => 'send-trackbacks',
	),
	'active' => true,
	'description' => '',
	'show_in_rest' => false,
) );
} );

