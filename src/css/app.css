/* Web fonts */
@font-face {
    font-family: "SetupGrotesk-Regular";
    src: url("/wp-content/themes/scd/fonts/webfonts/SetupGrotesk/SetupGrotesk-Regular.woff2") format("woff2"), url("/wp-content/themes/scd/fonts/webfonts/SetupGrotesk/SetupGrotesk-Regular.woff") format("woff");
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "SetupGrotesk-Medium";
    src: url("/wp-content/themes/scd/fonts/webfonts/SetupGrotesk/SetupGrotesk-Medium.woff2") format("woff2"), url("/wp-content/themes/scd/fonts/webfonts/SetupGrotesk/SetupGrotesk-Medium.woff") format("woff");
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "SetupGrotesk-Bold";
    src: url("/wp-content/themes/scd/fonts/webfonts/SetupGrotesk/SetupGrotesk-Bold.woff2") format("woff2"), url("/wp-content/themes/scd/fonts/webfonts/SetupGrotesk/SetupGrotesk-Bold.woff") format("woff");
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

/* GRID switcher */
.grid-switcher {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    background-color: #363636;
    border-radius: 50%;
    bottom: 10px;
    color: #fff;
    cursor: pointer;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    font-size: 10px;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    left: 10px;
    position: fixed;
    width: 40px;
    height: 40px;
    text-transform: uppercase;
    -webkit-transition: opacity 0.25s ease-in-out;
    -o-transition: opacity 0.25s ease-in-out;
    transition: opacity 0.25s ease-in-out;
    z-index: 99999999;
    font-family: "SetupGrotesk-Regular", Arial, sans-serif;
}

.grid-switcher.is-active, .grid-switcher:focus, .grid-switcher:hover {
    color: #000000;
    background-color: #ffe5e5;
}

.grid-container {
    font-size: 0;
    pointer-events: none;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    position: fixed;
    z-index: 9999999;
    opacity: 0;
    overflow: hidden;
    -webkit-transition: opacity 600ms ease;
    -o-transition: opacity 600ms ease;
    transition: opacity 600ms ease;
}

.grid-container.is-active {
    opacity: 1;
    overflow: visible;
    -webkit-transition: opacity 600ms ease;
    -o-transition: opacity 600ms ease;
    transition: opacity 600ms ease;
}

.grid-column-item {
    width: 100%;
    min-width: 1px;
    min-height: 0;
    width: calc(100% / 6);
    -webkit-box-flex: 0;
    -ms-flex: 0 0 calc(100% / 6);
    flex: 0 0 calc(100% / 6);
    max-width: calc(100% / 6);
}

@media (min-width: 771px) {
    .grid-column-item {
        width: calc(100% / 12);
        -webkit-box-flex: 0;
        -ms-flex: 0 0 calc(100% / 12);
        flex: 0 0 calc(100% / 12);
        max-width: calc(100% / 12);
    }
}

.grid-column {
    background-color: rgba(255, 0, 0, 0.1);
    height: 100vh;
}

/* Animate helpers */
@-webkit-keyframes spinner-rotate {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes spinner-rotate {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

.loading-spinner {
    display: inline-block;
    width: 50px;
    height: 50px;
    -webkit-animation-name: spinner-rotate;
    animation-name: spinner-rotate;
    -webkit-animation-duration: 0.75s;
    animation-duration: 0.75s;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    border: 10px solid #000000;
    border-right-color: transparent;
    border-radius: 50%;
}

.donut-spinner {
    display: inline-block;
    border: 10px solid #e6edff;
    border-left-color: #000000;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    -webkit-animation: donut-spin 1.2s linear infinite;
    animation: donut-spin 1.2s linear infinite;
}

@-webkit-keyframes donut-spin {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes donut-spin {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

.bouncing-loader > div,
.bouncing-loader:before,
.bouncing-loader:after {
    display: inline-block;
    width: 13px;
    height: 13px;
    background: #000000;
    margin-bottom: -5px;
    border-radius: 50%;
    -webkit-animation: bouncing-loader 0.6s infinite alternate;
    animation: bouncing-loader 0.6s infinite alternate;
}

.bouncing-loader > div,
.bouncing-loader:before,
.bouncing-loader:after {
    content: '';
}

.bouncing-loader > div {
    margin: 0 5px;
}

.bouncing-loader > div {
    -webkit-animation-delay: 0.2s;
    animation-delay: 0.2s;
}

.bouncing-loader:after {
    -webkit-animation-delay: 0.4s;
    animation-delay: 0.4s;
}

@-webkit-keyframes bouncing-loader {
    to {
        opacity: 0.1;
        -webkit-transform: translate3d(0, -16px, 0);
        transform: translate3d(0, -16px, 0);
    }
}

@keyframes bouncing-loader {
    to {
        opacity: 0.1;
        -webkit-transform: translate3d(0, -16px, 0);
        transform: translate3d(0, -16px, 0);
    }
}

@-webkit-keyframes slideOutLeft {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
    }
    to {
        visibility: hidden;
        -webkit-transform: translate3d(-40%, 0, 0);
        transform: translate3d(-40%, 0, 0);
    }
}

@keyframes slideOutLeft {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
    }
    to {
        visibility: hidden;
        -webkit-transform: translate3d(-60%, 0, 0);
        transform: translate3d(-60%, 0, 0);
    }
}

/* Typography */
.typography p,
.typography li,
.typography td,
.typography th {
    line-height: 1.5em;
}

.typography p,
.typography ul,
.typography ol,
.typography h1,
.typography h2,
.typography h3,
.typography h4,
.typography h5,
.typography h6,
.typography table,
.typography fieldset,
.typography blockquote,
.typography iframe {
    margin-bottom: 1rem;
}

.typography h1 {
    display: block;
    margin-top: 0;
    margin-bottom: 2rem;
    font-family: "SetupGrotesk-Bold", Arial, sans-serif;
    font-weight: 700;
    color: #000000;
    font-size: 22px;
    font-size: 1.375rem;
}

@media (min-width: 771px) {
    .typography h1 {
        font-size: 32px;
        font-size: 2rem;
    }
}

.typography h2 {
    display: block;
    margin-top: 0;
    margin-bottom: 1.2rem;
    font-family: "SetupGrotesk-Bold", Arial, sans-serif;
    font-weight: 700;
    color: #000000;
    font-size: 18px;
    font-size: 1.125rem;
}

@media (min-width: 771px) {
    .typography h2 {
        font-size: 22px;
        font-size: 1.375rem;
    }
}

.typography h3 {
    display: block;
    margin-top: 0;
    margin-bottom: 1rem;
    font-family: "SetupGrotesk-Bold", Arial, sans-serif;
    font-weight: 700;
    color: #000000;
    font-size: 18px;
    font-size: 1.125rem;
}

@media (min-width: 771px) {
    .typography h3 {
        font-size: 24px;
        font-size: 1.5rem;
    }
}

.typography h4 {
    display: block;
    margin-top: 0;
    margin-bottom: 1.2rem;
    font-family: "SetupGrotesk-Bold", Arial, sans-serif;
    font-weight: 700;
    color: #000000;
    font-size: 20px;
    font-size: 1.25rem;
}

@media (min-width: 771px) {
    .typography h4 {
        font-size: 22px;
        font-size: 1.375rem;
    }
}

.typography h5 {
    display: block;
    margin-top: 0;
    margin-bottom: 1rem;
    font-family: "SetupGrotesk-Bold", Arial, sans-serif;
    font-weight: 700;
    color: #000000;
    font-size: 18px;
    font-size: 1.125rem;
}

@media (min-width: 771px) {
    .typography h5 {
        font-size: 20px;
        font-size: 1.25rem;
    }
}

.typography h6 {
    display: block;
    margin-top: 0;
    margin-bottom: 1rem;
    font-family: "SetupGrotesk-Bold", Arial, sans-serif;
    font-weight: 700;
    color: #000000;
    font-size: 18px;
    font-size: 1.125rem;
}

@media (min-width: 771px) {
    .typography h6 {
        font-size: 18px;
        font-size: 1.125rem;
    }
}

.typography a {
    text-decoration: underline;
    color: #000000;
}

.typography a:focus, .typography a:hover {
    text-decoration: underline;
}

.typography p {
    display: block;
    margin-top: 0;
    margin-bottom: 2rem;
    font-family: "SetupGrotesk-Regular", Arial, sans-serif;
    font-weight: 400;
    color: #000000;
    font-size: 16px;
    font-size: 1rem;
    line-height: 1.8;
}

@media (min-width: 771px) {
    .typography p {
        line-height: 2.3;
    }
}

.typography .subheadline {
    font-weight: 400; /* perex fix */
    line-height: 1.6;
    font-size: 18px;
    font-size: 1.125rem;
}

@media (min-width: 771px) {
    .typography .subheadline {
        font-size: 22px;
        font-size: 1.375rem;
    }
}

.typography strong {
    font-weight: 700;
    color: #000000;
}

.typography img {
    max-width: 100%;
    margin-bottom: 2rem;
    height: auto;
}

.typography ul,
.typography ol {
    margin-bottom: 2rem;
    padding-left: 0;
    list-style: none;
}

.typography ul li:not(figure ul li),
.typography ol li:not(figure ol li) {
    position: relative;
    margin-bottom: 0.3rem;
    padding-left: 2rem;
    font-family: "SetupGrotesk-Regular", Arial, sans-serif;
    font-weight: 400;
    line-height: 1.6;
    color: #000000;
    font-size: 16px;
    font-size: 1rem;
}

.typography ul li:not(figure ul li):before {
    position: absolute;
    top: 9px;
    left: 3px;
    display: block;
    width: 6px;
    height: 6px;
    content: '';
    color: #000000;
    border-radius: 100%;
    background-color: #000000;
}

.typography ol:not(figure ol) {
    list-style-position: inside;
    counter-reset: item;
}

.typography ol li:not(figure ol li):before {
    position: absolute;
    top: 1px;
    left: 0;
    display: inline-block;
    line-height: 1.4;
    content: counter(item) ".";
    counter-increment: item;
    color: #000000;
    font-size: 16px;
    font-size: 1rem;
}

.typography figure {
    margin: 0;
    margin-bottom: 3rem;
}

.typography figure img {
    width: 100%;
    margin: 0;
}

.typography figcaption {
    text-align: center;
    font-family: "SetupGrotesk-Regular", Arial, sans-serif;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.5);
    font-size: 16px;
    font-size: 1rem;
    max-width: 650px;
    margin: 1.2rem auto 0;
    line-height: 1.8;
}

@media (min-width: 771px) {
    .typography figcaption {
        line-height: 2.3;
    }
}

@media (min-width: 771px) {
    .typography .figure-two-columns figure {
        height: calc(50% - 15px);
        margin-bottom: 1.875rem;
    }
}

.typography .figure-two-columns figure img {
    height: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}

.typography .figure-two-columns figure:last-child {
    margin-bottom: 0;
}

@media (min-width: 771px) {
    .typography .figure-single-columns figure {
        height: 100%;
        margin-bottom: 0;
    }
}

.typography .figure-single-columns img {
    height: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}

.detail-magazin {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    background: #f6f6f6;
    margin-bottom: 2.8125rem;
}

.detail-magazin__image-block {
    width: 145px;
    max-width: 145px;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 145px;
    flex: 0 0 145px;
}

@media (min-width: 576px) {
    .detail-magazin__image-block {
        width: 180px;
        max-width: 180px;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 180px;
        flex: 0 0 180px;
    }
}

.detail-magazin__text-block {
    width: auto;
    max-width: auto;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    -ms-flex-item-align: center;
    align-self: center;
    padding: 0.9375rem 0.625rem;
}

@media (min-width: 576px) {
    .detail-magazin__text-block {
        padding: 0.9375rem 1.25rem;
    }
}

.detail-magazin__text-block p {
    margin: 0;
    line-height: 1.5;
}

.detail-magazin__title.gamma {
    margin-bottom: 0;
    font-size: 16px;
    font-size: 1rem;
}

@media (min-width: 576px) {
    .detail-magazin__title.gamma {
        font-size: 22px;
        font-size: 1.375rem;
        margin-bottom: 1rem;
    }
}

.iframe-responsive {
    position: relative;
    overflow: hidden;
    width: 100%;
    padding-top: 56.25%;
}

.iframe-responsive iframe {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 100%;
}

/* Form base */
.form__input, .form__textarea {
    width: 100%;
    height: 55px;
    padding: 0.2rem 1rem;
    border: 1px solid #000000;
    background-color: #ffffff;
    color: #000000;
    font-family: "SetupGrotesk-Regular", Arial, sans-serif;
    font-weight: 400;
    font-size: 18px;
    font-size: 1.125rem;
    border-radius: 0;
    outline: none !important;
    -webkit-appearance: none;
}

.form__input:-moz-placeholder-shown, .form__textarea:-moz-placeholder-shown {
    border-color: #000000;
}

.form__input:-ms-input-placeholder, .form__textarea:-ms-input-placeholder {
    border-color: #000000;
}

.form__input:placeholder-shown, .form__textarea:placeholder-shown {
    border-color: #000000;
}

.form__input:focus, .form__textarea:focus {
    border-color: #000000;
}

.form__input--newsletter {
    padding-right: 150px;
}

.form__textarea {
    height: 150px;
    padding: 1rem 1.5rem;
    line-height: 1.5rem;
    resize: none;
    border-radius: 0;
}

@media (min-width: 576px) {
    .form__textarea--limit {
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        resize: none;
        min-height: 100px;
        max-height: 250px;
    }
}

@media (max-width: 575px) {
    .form__textarea--limit {
        height: 200px !important;
    }
}

.form__select {
    width: 100%;
    height: 60px;
    height: 55px;
    padding: 0.2rem 1rem;
    font-family: "SetupGrotesk-Bold", Arial, sans-serif;
    font-weight: 400;
    font-size: 18px;
    font-size: 1.125rem;
    border-radius: 0;
    line-height: 1.2;
    color: #000000;
    border: 1px solid #000000;
    outline: none;
    background-color: #ffffff;
    -webkit-appearance: value;
    -moz-appearance: value;
    -ms-appearance: value;
    -webkit-appearance: none;
    -moz-appearance: none;
    -ms-appearance: none;
    appearance: none;
}

.form__icon {
    position: absolute;
    top: 0;
    right: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    width: 60px;
    height: 60px;
    font-size: 22px;
    font-size: 1.375rem;
    color: #000000;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    pointer-events: none;
}

.form__icon-block .form__input {
    padding-right: 60px;
}

.form__label {
    display: block;
    margin: 0 0 10px;
    font-family: "SetupGrotesk-Regular", Arial, sans-serif;
    font-weight: 400;
    color: #000000;
    font-size: 16px;
    font-size: 1rem;
}

.form__field {
    position: relative;
}

.form__field--small {
    margin-bottom: 0.5rem;
}

.form__field--medium {
    margin-bottom: 1rem;
}

.form__field--big {
    margin-bottom: 2rem;
}

.form__placeholder {
    position: relative;
    z-index: 1;
}

.form__placeholder.is-active .form__label {
    width: calc(100% - 20px);
    -webkit-transform: translateX(1px) translateY(1px);
    -ms-transform: translateX(1px) translateY(1px);
    transform: translateX(1px) translateY(1px);
    font-size: 12px;
    font-size: 0.75rem;
    padding: 10px 25px 5px;
}

.form__placeholder .form__input,
.form__placeholder .form__textarea {
    padding: 25px 25px 13px;
}

.form__placeholder .select2-container .select2-selection--single {
    padding: 7px 40px 0 25px;
}

.form__placeholder .form__label {
    position: absolute;
    z-index: 10;
    top: 0;
    left: 0;
    margin: 0;
    cursor: text;
    -webkit-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
    -webkit-transform: translateX(1px) translateY(8px);
    -ms-transform: translateX(1px) translateY(8px);
    transform: translateX(1px) translateY(8px);
    text-transform: none;
    pointer-events: none;
    padding: 15px 25px 10px;
}

.form__placeholder-textarea .form__label {
    top: 1px;
    left: 2px;
    background: white;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
    -webkit-box-shadow: 0 0 0px 1000px transparent inset;
    -webkit-transition: background-color 5000s ease-in-out 0s;
    transition: background-color 5000s ease-in-out 0s;
}

.clear-filter-block .clear-filter-content {
    display: none;
}

.clear-filter-block.is-selected .clear-filter-content {
    display: block;
}

/* Placeholder helpers */
input::-webkit-input-placeholder, select::-webkit-input-placeholder, textarea::-webkit-input-placeholder {
    color: #6f6f6f;
}

input::-moz-placeholder, select::-moz-placeholder, textarea::-moz-placeholder {
    color: #6f6f6f;
}

input:-ms-input-placeholder, select:-ms-input-placeholder, textarea:-ms-input-placeholder {
    color: #6f6f6f;
}

input::-ms-input-placeholder, select::-ms-input-placeholder, textarea::-ms-input-placeholder {
    color: #6f6f6f;
}

input::placeholder,
select::placeholder,
textarea::placeholder {
    color: #6f6f6f;
}

input::-webkit-input-placeholder,
select::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
    color: #6f6f6f;
}

input::-moz-input-placeholder,
select::-moz-input-placeholder,
textarea::-moz-input-placeholder {
    color: #6f6f6f;
}

input:-ms-input-placeholder,
select:-ms-input-placeholder,
textarea:-ms-input-placeholder {
    color: #6f6f6f;
}

.form__field--white input::-webkit-input-placeholder, .form__field--white select::-webkit-input-placeholder, .form__field--white textarea::-webkit-input-placeholder {
    color: white;
}

.form__field--white input::-moz-placeholder, .form__field--white select::-moz-placeholder, .form__field--white textarea::-moz-placeholder {
    color: white;
}

.form__field--white input:-ms-input-placeholder, .form__field--white select:-ms-input-placeholder, .form__field--white textarea:-ms-input-placeholder {
    color: white;
}

.form__field--white input::-ms-input-placeholder, .form__field--white select::-ms-input-placeholder, .form__field--white textarea::-ms-input-placeholder {
    color: white;
}

.form__field--white input::placeholder,
.form__field--white select::placeholder,
.form__field--white textarea::placeholder {
    color: white;
}

.form__field--white input::-webkit-input-placeholder,
.form__field--white select::-webkit-input-placeholder,
.form__field--white textarea::-webkit-input-placeholder {
    color: white;
}

.form__field--white input::-moz-input-placeholder,
.form__field--white select::-moz-input-placeholder,
.form__field--white textarea::-moz-input-placeholder {
    color: white;
}

.form__field--white input:-ms-input-placeholder,
.form__field--white select:-ms-input-placeholder,
.form__field--white textarea:-ms-input-placeholder {
    color: white;
}

input:focus::-webkit-input-placeholder, textarea:focus::-webkit-input-placeholder {
    color: transparent;
    opacity: 1;
}

input:focus::-moz-placeholder, textarea:focus::-moz-placeholder {
    color: transparent;
    opacity: 1;
}

input:focus:-ms-input-placeholder, textarea:focus:-ms-input-placeholder {
    color: transparent;
    opacity: 1;
}

input:focus::-ms-input-placeholder, textarea:focus::-ms-input-placeholder {
    color: transparent;
    opacity: 1;
}

input:focus::placeholder,
textarea:focus::placeholder {
    color: transparent;
    opacity: 1;
}

input:focus::-webkit-input-placeholder,
textarea:focus::-webkit-input-placeholder {
    color: transparent;
    opacity: 1;
}

input:focus::-moz-input-placeholder,
textarea:focus::-moz-input-placeholder {
    color: transparent;
    opacity: 1;
}

input:focus:-moz-input-placeholder,
textarea:focus:-moz-input-placeholder {
    color: transparent;
    opacity: 1;
}

/* Form validation */
.has-field-error .form__field-message {
    display: block;
}

.has-field-error.select2-container .select2-selection--single,
.has-field-error.select-classic .select2-container .select2-selection--multiple, .has-field-error.form__textarea, .has-field-error.form__input {
    border-color: #ea4335 !important;
}

.has-field-error + .select2-container .select2-selection--single,
.has-field-error + .select-classic .select2-container .select2-selection--multiple {
    border-color: #ea4335 !important;
}

.has-field-success.select2-container .select2-selection--single,
.has-field-success.select-classic .select2-container .select2-selection--multiple, .has-field-success.form__textarea, .has-field-success.form__input {
    border-color: #55a954 !important;
}

.has-field-success + .select2-container .select2-selection--single,
.has-field-success + .select-classic .select2-container .select2-selection--multiple {
    border-color: #55a954 !important;
}

.parsley-error.select2-container .select2-selection--single,
.parsley-error.select-classic .select2-container .select2-selection--multiple, .parsley-error.form__textarea, .parsley-error.form__input,
.has-error.select2-container .select2-selection--single,
.has-error.select-classic .select2-container .select2-selection--multiple,
.has-error.form__textarea,
.has-error.form__input {
    border-color: #ea4335 !important;
}

.parsley-error + .select2-container .select2-selection--single,
.parsley-error + .select-classic .select2-container .select2-selection--multiple,
.has-error + .select2-container .select2-selection--single,
.has-error + .select-classic .select2-container .select2-selection--multiple {
    border-color: #ea4335 !important;
}

.has-field-success.select2-container .select2-selection--single,
.has-field-success.select-classic .select2-container .select2-selection--multiple, .has-field-success.form__textarea, .has-field-success.form__input,
.parsley-success.select2-container .select2-selection--single,
.parsley-success.select-classic .select2-container .select2-selection--multiple,
.parsley-success.form__textarea,
.parsley-success.form__input {
    border-color: #55a954 !important;
}

.has-field-success + .select2-container .select2-selection--single,
.has-field-success + .select-classic .select2-container .select2-selection--multiple,
.parsley-success + .select2-container .select2-selection--single,
.parsley-success + .select-classic .select2-container .select2-selection--multiple {
    border-color: #55a954 !important;
}

.form__field-message {
    display: none;
    margin-top: 5px;
    color: #870000;
    text-align: right;
    font-size: 14px;
    font-size: 0.875rem;
}

.parsley-errors-list {
    color: #ea4335;
    text-align: right;
    font-size: 14px;
    font-size: 0.875rem;
}

.parsley-errors-list li {
    display: block;
    margin-top: 5px;
}

/* .checkbox.checkbox--radiobox
	input#checkbox_payment_1.checkbox__input-hidden(type='radio', name="payment" required='')
	label.checkbox__label(for='checkbox_payment_1')
		span.checkbox__box
		span.checkbox__text Platba kartou */
/* Checkbox helpers */
.checkbox {
    margin-bottom: 0.6rem;
}

.input-hidden,
.checkbox__input-hidden {
    position: absolute;
    opacity: 0;
    z-index: -1;
}

.checkbox__label {
    margin: 0;
    padding-left: 0;
    min-height: inherit;
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    vertical-align: middle;
    padding-top: 0;
    cursor: pointer;
    text-transform: none;
}

.checkbox__box {
    position: relative;
    max-width: 20px;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 20px;
    flex: 0 0 20px;
    width: 20px;
    height: 20px;
    border: 1px solid #000000;
    cursor: pointer;
    display: inline-block;
    vertical-align: middle;
    margin-left: 0;
}

.checkbox__text {
    max-width: calc(100% - 20px);
    -webkit-box-flex: 0;
    -ms-flex: 0 0 calc(100% - 20px);
    flex: 0 0 calc(100% - 20px);
    width: calc(100% - 20px);
    display: block;
    position: relative;
    cursor: pointer;
    text-align: left;
    color: #000000;
    font-size: 16px;
    font-size: 1rem;
    padding-left: 1rem;
    line-height: 1.4;
}

.checkbox__slider {
    position: absolute;
    height: 18px;
    width: 18px;
    left: 2px;
    bottom: 2px;
    border-radius: 50% !important;
    background-color: #000000;
    -webkit-transition: all 0.2s ease;
    -o-transition: all 0.2s ease;
    transition: all 0.2s ease;
}

.checkbox__input-hidden:checked + .checkbox__label .checkbox__box:after {
    content: '';
    display: block;
    position: absolute;
    top: 2px;
    left: 6px;
    width: 6px;
    height: 12px;
    border: solid #000000;
    border-width: 0 2px 2px 0;
    -webkit-transform: rotate(40deg);
    -ms-transform: rotate(40deg);
    transform: rotate(40deg);
}

.checkbox--switcher {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 26px;
}

.checkbox--switcher .checkbox__label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px solid #000000;
    border-radius: 35px;
    background-color: transparent;
}

.checkbox--switcher .checkbox__input-hidden:checked + .checkbox__label {
    border-color: #000000;
}

.checkbox--switcher .checkbox__input-hidden:checked + .checkbox__label:after {
    display: none;
}

.checkbox--switcher .checkbox__input-hidden:checked + .checkbox__label .checkbox__slider {
    background-color: #000000;
    left: calc(100% - 20px);
}

.checkbox--radiobox {
    margin-bottom: 2rem;
}

.checkbox--radiobox .checkbox__box {
    top: 0;
    max-width: 20px;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 20px;
    flex: 0 0 20px;
    width: 20px;
    height: 20px;
    border: 2px solid #000000;
    border-radius: 100%;
}

.checkbox--radiobox .checkbox__input-hidden:checked + .checkbox__label .checkbox__box:after {
    content: '';
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    border-radius: 100%;
    margin-top: -5px;
    margin-left: -5px;
    width: 10px;
    height: 10px;
    background-color: #000000;
}

/* Form quantity */
.quantity {
    position: relative;
}

.quantity__input {
    text-align: center;
    padding-left: 65px;
    padding-right: 65px;
    outline: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -o-user-select: none;
    user-select: none;
    text-align: center;
}

.quantity__increment,
.quantity__decrement {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    line-height: 60px;
    text-align: center;
    position: absolute;
    top: 0;
    cursor: pointer;
}

.quantity__increment:after, .quantity__increment:before,
.quantity__decrement:after,
.quantity__decrement:before {
    content: '' !important;
    width: 19px;
    height: 28px;
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
}

.quantity__increment svg,
.quantity__decrement svg {
    fill: #000000;
}

.quantity__increment {
    right: 0;
}

.quantity__increment:before {
    background-image: url("/wp-content/themes/scd/assets/icons/chevron-right.svg");
}

.quantity__decrement {
    left: 0;
}

.quantity__decrement:after {
    background-image: url("/wp-content/themes/scd/assets/icons/chevron-left.svg");
}

input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type='number'] {
    -moz-appearance: textfield;
}

/* Border helpers */
.border-0 {
    border: 0 !important;
}

.border-0-left {
    border-left: 0 !important;
}

.border-0-right {
    border-right: 0 !important;
}

.border-0-bottom {
    border-bottom: 0 !important;
}

.border-0-top {
    border-top: 0 !important;
}

@media (min-width: 576px) {
    .border-0-xs-left {
        border-left: 0 !important;
    }
}

@media (min-width: 576px) {
    .border-0-xs-bottom {
        border-bottom: 0 !important;
    }
}

@media (min-width: 992px) {
    .border-0-md-left {
        border-left: 0 !important;
    }
}

@media (min-width: 992px) {
    .border-0-md-right {
        border-right: 0 !important;
    }
}

@media (min-width: 992px) {
    .border-0-md-top {
        border-top: 0 !important;
    }
}

@media (min-width: 992px) {
    .border-0-md-bottom {
        border-bottom: 0 !important;
    }
}

@media (max-width: 991px) {
    .border-bp-0-md-top {
        border-top: 0 !important;
    }
}

/* Buttons helper */
.btn {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    position: relative;
    margin: 0;
    padding: 1em 1.2rem;
    font-family: "SetupGrotesk-Bold", Arial, sans-serif;
    font-weight: 700;
    line-height: 1.2;
    cursor: pointer;
    min-height: 55px;
    text-decoration: none;
    text-transform: uppercase;
    border: 0;
    outline: none !important;
    background: #000000;
    text-align: center;
    -webkit-transition: all 500ms ease;
    -o-transition: all 500ms ease;
    transition: all 500ms ease;
    border-radius: 0;
    font-size: 16px;
    font-size: 1rem;
    -webkit-appearance: none;
}

.btn, .btn:visited, .btn:active, .btn:focus {
    color: white;
}

.btn:hover {
    -webkit-transition: all 500ms ease;
    -o-transition: all 500ms ease;
    transition: all 500ms ease;
}

@media (min-width: 992px) {
    .btn:hover {
        cursor: pointer;
        text-decoration: none;
        color: white;
    }
}

.btn--medium {
    font-family: "SetupGrotesk-Medium", Arial, sans-serif;
    font-weight: 500;
}

.btn--responsive {
    width: 100%;
    padding-right: 0;
    padding-left: 0;
    text-align: center;
}

.btn--font-size-xsmall {
    font-size: 12px;
    font-size: 0.75rem;
}

.btn--font-size-small {
    font-size: 14px;
    font-size: 0.875rem;
}

.btn--font-size-medium {
    font-size: 16px;
    font-size: 1rem;
}

.btn--font-size-big {
    font-size: 18px;
    font-size: 1.125rem;
}

.btn--font-size-large {
    font-size: 20px;
    font-size: 1.25rem;
}

.btn--size-small {
    min-height: 45px;
    padding: 0.6em 0.8rem;
    font-size: 12px;
    font-size: 0.75rem;
}

@media (min-width: 576px) {
    .btn--size-small {
        font-size: 14px;
        font-size: 0.875rem;
        padding: 0.6em 1.2rem;
    }
}

.btn--size-medium {
    padding: 1.2em 1.5rem;
}

.btn--size-big {
    padding: 2.2em 2rem;
}

.btn--min-width-xsmall {
    min-width: 140px;
}

.btn--min-width-small {
    min-width: 160px;
}

.btn--min-width-medium {
    min-width: 220px;
}

.btn--min-width-big {
    min-width: 300px;
}

.btn--absolute {
    position: absolute;
    top: 0;
    right: 0;
}

.btn--absolute-ext {
    position: absolute;
    top: 1.375rem;
    right: 1.25rem;
}

@media (max-width: 575px) {
    .btn--absolute-ext {
        min-height: 35px;
        padding: 0.4em 0.2rem;
        min-width: 130px;
        font-size: 12px;
        font-size: 0.75rem;
    }
}

@media (min-width: 576px) {
    .btn--absolute-ext {
        top: 1.5625rem;
        right: 1.875rem;
    }
}

@media (max-width: 575px) {
    .btn--newsletter {
        min-width: 90px;
        padding-left: 1.2rem;
        padding-right: 1.2rem;
    }
}

@media (min-width: 992px) {
    .btn--offset-top {
        position: relative;
        top: -1px;
    }
}

.btn--black-fill {
    color: #ffffff;
    border: 1px solid transparent;
    background-color: #000000;
}

.btn--black-fill, .btn--black-fill:visited, .btn--black-fill:active, .btn--black-fill:focus {
    color: #ffffff;
}

.btn--black-fill:hover {
    border: 1px solid #000000;
    color: #000000;
    background-color: #ffffff;
}

.btn--white-fill {
    color: #000000;
    border: 1px solid #000000;
    background-color: #ffffff;
}

.btn--white-fill, .btn--white-fill:visited, .btn--white-fill:active, .btn--white-fill:focus {
    color: #000000;
}

.btn--white-fill:hover {
    border: 1px solid #000000;
    color: #ffffff;
    background-color: #000000;
}

.btn--black-outline {
    color: #000000;
    border: 2px solid #000000;
    background-color: transparent;
}

.btn--black-outline, .btn--black-outline:visited, .btn--black-outline:active, .btn--black-outline:focus {
    color: #000000;
}

.btn--black-outline:hover {
    color: #ffffff;
    border: 2px solid #000000;
    background-color: #000000;
}

.btn--black-outline:hover .btn__icon {
    color: #ffffff;
}

.btn--black-outline .btn__icon {
    color: #000000;
}

/* Color helpers */
.bg-black {
    background-color: #000000;
}

.bg-purple {
    background-color: #000000;
}

.bg-gray-light {
    background-color: #f7f7f7;
}

.txt-white {
    color: #ffffff;
}

.txt-black {
    color: #000000;
}

.h--white h1,
.h--white .alfa,
.h--white h2,
.h--white .beta,
.h--white h3,
.h--white .gamma,
.h--white h4,
.h--white .delta,
.h--white h5,
.h--white .epsilon,
.h--white h6,
.h--white .zeta {
    color: #ffffff;
}

.h--purple h1,
.h--purple .alfa,
.h--purple h2,
.h--purple .beta,
.h--purple h3,
.h--purple .gamma,
.h--purple h4,
.h--purple .delta,
.h--purple h5,
.h--purple .epsilon,
.h--purple h6,
.h--purple .zeta {
    color: #000000;
}

.p--white p {
    color: #ffffff;
}

.p--black p {
    color: #000000;
}

.p--purple p {
    color: #000000;
}

ul.list-style.ul--white li,
.ul--white li {
    color: #ffffff;
}

ul.list-style.ul--white li:before,
.ul--white li:before {
    background-color: #ffffff;
}

ol.list-style.ol-white li,
.ol-white li {
    color: #ffffff;
}

ol.list-style.ol-white li:before,
.ol-white li:before {
    color: #ffffff;
}

/* Gutter helpers */
.no-gutter {
    margin: 0;
}

.no-gutter > [class*="col-"] {
    padding-right: 0;
    padding-left: 0;
}

.gutter-4.row {
    margin-right: -2px;
    margin-left: -2px;
}

.gutter-4 > [class*="col-"],
.gutter-4 > [class*=" col-"] {
    padding-right: 2px;
    padding-left: 2px;
}

.gutter-6.row {
    margin-right: -3px;
    margin-left: -3px;
}

.gutter-6 > [class*="col-"],
.gutter-6 > [class*=" col-"] {
    padding-right: 3px;
    padding-left: 3px;
}

.gutter-10.row {
    margin-right: -5px;
    margin-left: -5px;
}

.gutter-10 > [class*="col-"],
.gutter-10 > [class*=" col-"] {
    padding-right: 5px;
    padding-left: 5px;
}

.gutter-20.row {
    margin-right: -10px;
    margin-left: -10px;
}

.gutter-20 > [class*="col-"],
.gutter-20 > [class*=" col-"] {
    padding-right: 10px;
    padding-left: 10px;
}

.gutter-30.row {
    margin-right: -15px;
    margin-left: -15px;
}

.gutter-30 > [class*="col-"],
.gutter-30 > [class*=" col-"] {
    padding-right: 15px;
    padding-left: 15px;
}

.gutter-40.row {
    margin-right: -20px;
    margin-left: -20px;
}

.gutter-40 > [class*="col-"],
.gutter-40 > [class*=" col-"] {
    padding-right: 20px;
    padding-left: 20px;
}

.gutter-50.row {
    margin-right: -25px;
    margin-left: -25px;
}

.gutter-50 > [class*="col-"],
.gutter-50 > [class*=" col-"] {
    padding-right: 25px;
    padding-left: 25px;
}

.gutter-60.row {
    margin-right: -30px;
    margin-left: -30px;
}

.gutter-60 > [class*="col-"],
.gutter-60 > [class*=" col-"] {
    padding-right: 30px;
    padding-left: 30px;
}

.gutter-sm-80.row {
    margin-right: -40px;
    margin-left: -40px;
}

.gutter-sm-80 > [class*="col-"],
.gutter-sm-80 > [class*=" col-"] {
    padding-right: 40px;
    padding-left: 40px;
}

.gutter-sm-100.row {
    margin-right: -50px;
    margin-left: -50px;
}

.gutter-sm-100 > [class*="col-"],
.gutter-sm-100 > [class*=" col-"] {
    padding-right: 50px;
    padding-left: 50px;
}

@media (min-width: 576px) {
    .gutter-xs-10.row {
        margin-right: -5px;
        margin-left: -5px;
    }

    .gutter-xs-10 > [class*="col-"],
    .gutter-xs-10 > [class*=" col-"] {
        padding-right: 5px;
        padding-left: 5px;
    }

    .gutter-xs-20.row {
        margin-right: -10px;
        margin-left: -10px;
    }

    .gutter-xs-20 > [class*="col-"],
    .gutter-xs-20 > [class*=" col-"] {
        padding-right: 10px;
        padding-left: 10px;
    }

    .gutter-xs-30.row {
        margin-right: -15px;
        margin-left: -15px;
    }

    .gutter-xs-30 > [class*="col-"],
    .gutter-xs-30 > [class*=" col-"] {
        padding-right: 15px;
        padding-left: 15px;
    }

    .gutter-xs-40.row {
        margin-right: -20px;
        margin-left: -20px;
    }

    .gutter-xs-40 > [class*="col-"],
    .gutter-xs-40 > [class*=" col-"] {
        padding-right: 20px;
        padding-left: 20px;
    }

    .gutter-xs-50.row {
        margin-right: -25px;
        margin-left: -25px;
    }

    .gutter-xs-50 > [class*="col-"],
    .gutter-xs-50 > [class*=" col-"] {
        padding-right: 25px;
        padding-left: 25px;
    }

    .gutter-xs-60.row {
        margin-right: -30px;
        margin-left: -30px;
    }

    .gutter-xs-60 > [class*="col-"],
    .gutter-xs-60 > [class*=" col-"] {
        padding-right: 30px;
        padding-left: 30px;
    }

    .gutter-xs-100.row {
        margin-right: -50px;
        margin-left: -50px;
    }

    .gutter-xs-100 > [class*="col-"],
    .gutter-xs-100 > [class*=" col-"] {
        padding-right: 50px;
        padding-left: 50px;
    }
}

@media (min-width: 771px) {
    .gutter-sm-10.row {
        margin-right: -5px;
        margin-left: -5px;
    }

    .gutter-sm-10 > [class*="col-"],
    .gutter-sm-10 > [class*=" col-"] {
        padding-right: 5px;
        padding-left: 5px;
    }

    .gutter-sm-20.row {
        margin-right: -10px;
        margin-left: -10px;
    }

    .gutter-sm-20 > [class*="col-"],
    .gutter-sm-20 > [class*=" col-"] {
        padding-right: 10px;
        padding-left: 10px;
    }

    .gutter-sm-30.row {
        margin-right: -15px;
        margin-left: -15px;
    }

    .gutter-sm-30 > [class*="col-"],
    .gutter-sm-30 > [class*=" col-"] {
        padding-right: 15px;
        padding-left: 15px;
    }

    .gutter-sm-40.row {
        margin-right: -20px;
        margin-left: -20px;
    }

    .gutter-sm-40 > [class*="col-"],
    .gutter-sm-40 > [class*=" col-"] {
        padding-right: 20px;
        padding-left: 20px;
    }

    .gutter-sm-50.row {
        margin-right: -25px;
        margin-left: -25px;
    }

    .gutter-sm-50 > [class*="col-"],
    .gutter-sm-50 > [class*=" col-"] {
        padding-right: 25px;
        padding-left: 25px;
    }

    .gutter-sm-60.row {
        margin-right: -30px;
        margin-left: -30px;
    }

    .gutter-sm-60 > [class*="col-"],
    .gutter-sm-60 > [class*=" col-"] {
        padding-right: 30px;
        padding-left: 30px;
    }

    .gutter-sm-100.row {
        margin-right: -50px;
        margin-left: -50px;
    }

    .gutter-sm-100 > [class*="col-"],
    .gutter-sm-100 > [class*=" col-"] {
        padding-right: 50px;
        padding-left: 50px;
    }
}

@media (min-width: 992px) {
    .no-md-gutter {
        margin: 0;
    }

    .no-md-gutter > [class*="col-"] {
        padding-right: 0;
        padding-left: 0;
    }

    .gutter-md-10.row {
        margin-right: -5px;
        margin-left: -5px;
    }

    .gutter-md-10 > [class*="col-"],
    .gutter-md-10 > [class*=" col-"] {
        padding-right: 5px;
        padding-left: 5px;
    }

    .gutter-md-20.row {
        margin-right: -10px;
        margin-left: -10px;
    }

    .gutter-md-20 > [class*="col-"],
    .gutter-md-20 > [class*=" col-"] {
        padding-right: 10px;
        padding-left: 10px;
    }

    .gutter-md-30.row {
        margin-right: -15px;
        margin-left: -15px;
    }

    .gutter-md-30 > [class*="col-"],
    .gutter-md-30 > [class*=" col-"] {
        padding-right: 15px;
        padding-left: 15px;
    }

    .gutter-md-40.row {
        margin-right: -20px;
        margin-left: -20px;
    }

    .gutter-md-40 > [class*="col-"],
    .gutter-md-40 > [class*=" col-"] {
        padding-right: 20px;
        padding-left: 20px;
    }

    .gutter-md-50.row {
        margin-right: -25px;
        margin-left: -25px;
    }

    .gutter-md-50 > [class*="col-"],
    .gutter-md-50 > [class*=" col-"] {
        padding-right: 25px;
        padding-left: 25px;
    }

    .gutter-md-60.row {
        margin-right: -30px;
        margin-left: -30px;
    }

    .gutter-md-60 > [class*="col-"],
    .gutter-md-60 > [class*=" col-"] {
        padding-right: 30px;
        padding-left: 30px;
    }

    .gutter-md-100.row {
        margin-right: -50px;
        margin-left: -50px;
    }

    .gutter-md-100 > [class*="col-"],
    .gutter-md-100 > [class*=" col-"] {
        padding-right: 50px;
        padding-left: 50px;
    }
}

@media (min-width: 1200px) {
    .gutter-lg-10.row {
        margin-right: -5px;
        margin-left: -5px;
    }

    .gutter-lg-10 > [class*="col-"],
    .gutter-lg-10 > [class*=" col-"] {
        padding-right: 5px;
        padding-left: 5px;
    }

    .gutter-lg-20.row {
        margin-right: -10px;
        margin-left: -10px;
    }

    .gutter-lg-20 > [class*="col-"],
    .gutter-lg-20 > [class*=" col-"] {
        padding-right: 10px;
        padding-left: 10px;
    }

    .gutter-lg-30.row {
        margin-right: -15px;
        margin-left: -15px;
    }

    .gutter-lg-30 > [class*="col-"],
    .gutter-lg-30 > [class*=" col-"] {
        padding-right: 15px;
        padding-left: 15px;
    }

    .gutter-lg-40.row {
        margin-right: -20px;
        margin-left: -20px;
    }

    .gutter-lg-40 > [class*="col-"],
    .gutter-lg-40 > [class*=" col-"] {
        padding-right: 20px;
        padding-left: 20px;
    }

    .gutter-lg-50.row {
        margin-right: -25px;
        margin-left: -25px;
    }

    .gutter-lg-50 > [class*="col-"],
    .gutter-lg-50 > [class*=" col-"] {
        padding-right: 25px;
        padding-left: 25px;
    }

    .gutter-lg-60.row {
        margin-right: -30px;
        margin-left: -30px;
    }

    .gutter-lg-60 > [class*="col-"],
    .gutter-lg-60 > [class*=" col-"] {
        padding-right: 30px;
        padding-left: 30px;
    }

    .gutter-lg-80.row {
        margin-right: -40px;
        margin-left: -40px;
    }

    .gutter-lg-80 > [class*="col-"],
    .gutter-lg-80 > [class*=" col-"] {
        padding-right: 40px;
        padding-left: 40px;
    }

    .gutter-lg-100.row {
        margin-right: -50px;
        margin-left: -50px;
    }

    .gutter-lg-100 > [class*="col-"],
    .gutter-lg-100 > [class*=" col-"] {
        padding-right: 50px;
        padding-left: 50px;
    }
}

@media (min-width: 1440px) {
    .gutter-xl-30.row {
        margin-right: -15px;
        margin-left: -15px;
    }

    .gutter-xl-30 > [class*="col-"],
    .gutter-xl-30 > [class*=" col-"] {
        padding-right: 15px;
        padding-left: 15px;
    }

    .gutter-xl-40.row {
        margin-right: -20px;
        margin-left: -20px;
    }

    .gutter-xl-40 > [class*="col-"],
    .gutter-xl-40 > [class*=" col-"] {
        padding-right: 20px;
        padding-left: 20px;
    }

    .gutter-xl-60.row {
        margin-right: -30px;
        margin-left: -30px;
    }

    .gutter-xl-60 > [class*="col-"],
    .gutter-xl-60 > [class*=" col-"] {
        padding-right: 30px;
        padding-left: 30px;
    }

    .gutter-xl-80.row {
        margin-right: -40px;
        margin-left: -40px;
    }

    .gutter-xl-80 > [class*="col-"],
    .gutter-xl-80 > [class*=" col-"] {
        padding-right: 40px;
        padding-left: 40px;
    }

    .gutter-xl-120.row {
        margin-right: -60px;
        margin-left: -60px;
    }

    .gutter-xl-120 > [class*="col-"],
    .gutter-xl-120 > [class*=" col-"] {
        padding-right: 60px;
        padding-left: 60px;
    }
}

@media (min-width: 1600px) {
    .gutter-xxl-30.row {
        margin-right: -15px;
        margin-left: -15px;
    }

    .gutter-xxl-30 > [class*="col-"],
    .gutter-xxl-30 > [class*=" col-"] {
        padding-right: 15px;
        padding-left: 15px;
    }

    .gutter-xxl-60.row {
        margin-right: -30px;
        margin-left: -30px;
    }

    .gutter-xxl-60 > [class*="col-"],
    .gutter-xxl-60 > [class*=" col-"] {
        padding-right: 30px;
        padding-left: 30px;
    }

    .gutter-xxl-120.row {
        margin-right: -60px;
        margin-left: -60px;
    }

    .gutter-xxl-120 > [class*="col-"],
    .gutter-xxl-120 > [class*=" col-"] {
        padding-right: 60px;
        padding-left: 60px;
    }

    .gutter-xxl-180.row {
        margin-right: -90px;
        margin-left: -90px;
    }

    .gutter-xxl-180 > [class*="col-"],
    .gutter-xxl-180 > [class*=" col-"] {
        padding-right: 90px;
        padding-left: 90px;
    }
}

@media (min-width: 1800px) {
    .gutter-xxxl-30.row {
        margin-right: -15px;
        margin-left: -15px;
    }

    .gutter-xxxl-30 > [class*="col-"],
    .gutter-xxxl-30 > [class*=" col-"] {
        padding-right: 15px;
        padding-left: 15px;
    }

    .gutter-xxxl-60.row {
        margin-right: -30px;
        margin-left: -30px;
    }

    .gutter-xxxl-60 > [class*="col-"],
    .gutter-xxxl-60 > [class*=" col-"] {
        padding-right: 30px;
        padding-left: 30px;
    }
}

/* Hidden content */
.hidden-content {
    height: 0;
    opacity: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-transition: opacity 250ms ease;
    -o-transition: opacity 250ms ease;
    transition: opacity 250ms ease;
}

.hidden-content.is-active {
    height: auto;
    opacity: 1;
    overflow: visible;
    visibility: visible;
    -webkit-transition: opacity 250ms ease;
    -o-transition: opacity 250ms ease;
    transition: opacity 250ms ease;
}

@media (max-width: 575px) {
    .hidden-xs-content {
        height: 0;
        opacity: 0;
        visibility: hidden;
        overflow: hidden;
        -webkit-transition: opacity 250ms ease;
        -o-transition: opacity 250ms ease;
        transition: opacity 250ms ease;
    }

    .hidden-xs-content.is-active {
        height: auto;
        opacity: 1;
        overflow: visible;
        visibility: visible;
        -webkit-transition: opacity 250ms ease;
        -o-transition: opacity 250ms ease;
        transition: opacity 250ms ease;
    }
}

@media (max-width: 770px) {
    .hidden-sm-content {
        height: 0;
        opacity: 0;
        visibility: hidden;
        overflow: hidden;
        -webkit-transition: opacity 250ms ease;
        -o-transition: opacity 250ms ease;
        transition: opacity 250ms ease;
    }

    .hidden-sm-content.is-active {
        height: auto;
        opacity: 1;
        overflow: visible;
        visibility: visible;
        -webkit-transition: opacity 250ms ease;
        -o-transition: opacity 250ms ease;
        transition: opacity 250ms ease;
    }
}

@media (max-width: 991px) {
    .hidden-md-content {
        height: 0;
        opacity: 0;
        visibility: hidden;
        overflow: hidden;
        -webkit-transition: opacity 250ms ease;
        -o-transition: opacity 250ms ease;
        transition: opacity 250ms ease;
    }

    .hidden-md-content.is-active {
        height: auto;
        opacity: 1;
        overflow: visible;
        visibility: visible;
        -webkit-transition: opacity 250ms ease;
        -o-transition: opacity 250ms ease;
        transition: opacity 250ms ease;
    }
}

@media (max-width: 1199px) {
    .hidden-lg-content {
        height: 0;
        opacity: 0;
        visibility: hidden;
        overflow: hidden;
        -webkit-transition: opacity 250ms ease;
        -o-transition: opacity 250ms ease;
        transition: opacity 250ms ease;
    }

    .hidden-lg-content.is-active {
        height: auto;
        opacity: 1;
        overflow: visible;
        visibility: visible;
        -webkit-transition: opacity 250ms ease;
        -o-transition: opacity 250ms ease;
        transition: opacity 250ms ease;
    }
}

/* Hidden class */
.invisible {
    visibility: hidden;
}

.visible {
    visibility: visible;
}

.hide {
    display: none;
}

.show {
    display: block;
}

.overflow-auto {
    overflow: auto;
}

.overflow-hidden {
    overflow: hidden;
}

.overflow-x-hidden {
    overflow-x: hidden;
}

.overflow-y-hidden {
    overflow-y: hidden;
}

.overflow-visible {
    overflow: visible;
}

/* HR helper */
.hr-line {
    margin: 30px 0;
    height: 1px;
    background-color: #f7f7f7;
}

.hr-line-1 {
    margin: 1rem 0;
}

.hr-line-1-3 {
    margin: 1rem 3rem;
}

.hr-line-2 {
    margin: 2rem 0;
}

.hr-line-3 {
    margin: 3rem 0;
}

.hr-line--width-100 {
    width: 100px;
}

.hr-line--width-200 {
    width: 200px;
}

.hr-line--width-300 {
    width: 300px;
}

.hr-line--center {
    margin: 0 auto;
}

.hr-line--black {
    background-color: #000000;
}

.hr-line--gray {
    background-color: #f7f7f7;
}

/* Icon helpers */
.icon-svg {
    display: inline-block;
    width: 1em;
    height: 1em;
    stroke-width: 0;
    stroke: currentColor;
    fill: currentColor;
    -webkit-transition: fill 0.15s;
    -o-transition: fill 0.15s;
    transition: fill 0.15s;
}

.icon--size-12 svg {
    font-size: 12px;
    font-size: 0.75rem;
}

.icon--size-14 svg {
    font-size: 14px;
    font-size: 0.875rem;
}

.icon--size-16 svg {
    font-size: 16px;
    font-size: 1rem;
}

.icon--size-18 svg {
    font-size: 18px;
    font-size: 1.125rem;
}

.icon--size-20 svg {
    font-size: 20px;
    font-size: 1.25rem;
}

.icon--size-22 svg {
    font-size: 22px;
    font-size: 1.375rem;
}

.icon--size-24 svg {
    font-size: 24px;
    font-size: 1.5rem;
}

.icon--size-26 svg {
    font-size: 26px;
    font-size: 1.625rem;
}

.icon--size-28 svg {
    font-size: 28px;
    font-size: 1.75rem;
}

.icon--size-30 svg {
    font-size: 30px;
    font-size: 1.875rem;
}

.icon--size-60 svg {
    font-size: 60px;
    font-size: 3.75rem;
}

.icon---white svg {
    fill: #ffffff;
}

.icon---black svg {
    fill: #000000;
}

.icon---purple svg {
    fill: #000000;
}

/* Image helpers */
.img {
    display: block;
}

.img--full {
    width: 100%;
}

.img--responsive {
    display: block;
    max-width: 100%;
    height: auto;
}

.img--cover {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}

.img--contain {
    height: 100%;
    -o-object-fit: contain;
    object-fit: contain;
}

.bg-image-hover,
.img-hover {
    display: block;
    overflow: hidden;
}

.bg-image-hover:focus > img, .bg-image-hover:hover > img,
.img-hover:focus > img,
.img-hover:hover > img {
    -webkit-transform: scale(1.05, 1.05);
    -ms-transform: scale(1.05, 1.05);
    transform: scale(1.05, 1.05);
}

.bg-image-hover img,
.img-hover img {
    -webkit-transition: -webkit-transform 400ms ease-out;
    transition: -webkit-transform 400ms ease-out;
    -o-transition: transform 400ms ease-out;
    transition: transform 400ms ease-out;
    transition: transform 400ms ease-out, -webkit-transform 400ms ease-out;
    will-change: transform;
}

.img-flex-contain,
.img-flex-cover {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}

.img-flex-contain img,
.img-flex-cover img {
    display: block;
    width: 100%;
    height: 100%;
}

.img-flex-cover img {
    -o-object-fit: cover;
    object-fit: cover;
}

.img-flex-contain img {
    -o-object-fit: contain;
    object-fit: contain;
}

.bg-image,
.bg-rectangle {
    display: block;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}

.bg-image {
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
}

/* Aspect ratio */
[class*='aspect-ratio-'] {
    display: block;
    position: relative;
}

[class*='aspect-ratio-'] > img {
    display: block;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}

@media (max-width: 991px) {
    [class*='aspect-md-ratio-'] > img {
        display: block;
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
    }

    .aspect-md-ratio-1-1 {
        padding-top: 100%;
    }
}

.aspect-ratio-16-9 {
    padding-top: 56.25%;
}

.aspect-ratio-3-2 {
    padding-top: 66.66%;
}

.aspect-ratio-4-3 {
    padding-top: 75%;
}

.aspect-ratio-8-5 {
    padding-top: 62.5%;
}

.aspect-ratio-1-1 {
    padding-top: 100%;
}

.aspect-ratio-23 {
    padding-top: 60%;
}

@media (min-width: 576px) {
    .aspect-ratio-23 {
        padding-top: 23%;
    }
}

.aspect-ratio-26 {
    padding-top: 60%;
}

@media (min-width: 576px) {
    .aspect-ratio-26 {
        padding-top: 26%;
    }
}

.aspect-ratio-26-ext {
    padding-top: 40%;
}

@media (min-width: 576px) {
    .aspect-ratio-26-ext {
        padding-top: 26%;
    }
}

.aspect-ratio-37 {
    height: 100%;
}

@media (min-width: 576px) {
    .aspect-ratio-37 {
        height: auto;
        padding-top: 37%;
    }
}

.aspect-ratio-50 {
    padding-top: 50%;
}

@media (min-width: 576px) {
    .aspect-ratio-50 {
        padding-top: 50%;
    }
}

.aspect-ratio-57 {
    height: 220px;
}

@media (min-width: 576px) {
    .aspect-ratio-57 {
        height: auto;
        padding-top: 57%;
    }
}

.aspect-ratio-41 {
    padding-top: 71%;
}

@media (min-width: 576px) {
    .aspect-ratio-41 {
        padding-top: 41%;
    }
}

.aspect-ratio-61 {
    padding-top: 67%;
}

@media (min-width: 576px) {
    .aspect-ratio-61 {
        padding-top: 61%;
    }
}

.aspect-ratio-71 {
    padding-top: 71%;
}

.aspect-ratio-77 {
    padding-top: 77%;
}

.aspect-ratio-80 {
    padding-top: 80%;
}

.aspect-ratio-88 {
    padding-top: 88%;
}

.aspect-ratio-81 {
    padding-top: 81%;
}

.aspect-ratio-103 {
    padding-top: 103%;
}

.aspect-ratio-103-ext {
    padding-top: 28%;
}

@media (min-width: 576px) {
    .aspect-ratio-103-ext {
        padding-top: 103%;
    }
}

.aspect-ratio-114 {
    height: 220px;
}

@media (min-width: 576px) {
    .aspect-ratio-114 {
        padding-top: 114%;
    }
}

.aspect-ratio-130 {
    padding-top: 130%;
}

@media (min-width: 576px) {
    .aspect-ratio-130 {
        padding-top: 130%;
    }
}

.aspect-ratio-xs-137 {
    padding-top: 137%;
}

@media (min-width: 576px) {
    .aspect-ratio-xs-137 {
        padding-top: 137%;
    }
}

.aspect-ratio-xs-1-1 {
    padding-top: 100%;
}

@media (min-width: 576px) {
    .aspect-ratio-xs-1-1 {
        padding-top: 100%;
    }
}

/* Margin helpers */
.margin-0 {
    margin: 0 !important;
}

@media (min-width: 576px) {
    .margin-xs-0 {
        margin: 0 !important;
    }
}

@media (min-width: 771px) {
    .margin-sm-0 {
        margin: 0 !important;
    }
}

@media (min-width: 992px) {
    .margin-md-0 {
        margin: 0 !important;
    }
}

@media (min-width: 1200px) {
    .margin-lg-0 {
        margin: 0 !important;
    }
}

@media (min-width: 1440px) {
    .margin-xl-0 {
        margin: 0 !important;
    }
}

.margin-bottom-01 {
    margin-bottom: 0.1rem;
}

.margin-bottom-02 {
    margin-bottom: 0.2rem;
}

.margin-bottom-03 {
    margin-bottom: 0.3rem;
}

.margin-bottom-04 {
    margin-bottom: 0.4rem;
}

.margin-bottom-05 {
    margin-bottom: 0.5rem;
}

.margin-bottom-06 {
    margin-bottom: 0.6rem;
}

.margin-bottom-07 {
    margin-bottom: 0.7rem;
}

.margin-bottom-08 {
    margin-bottom: 0.8rem;
}

.margin-bottom-09 {
    margin-bottom: 0.9rem;
}

.margin-bottom-1 {
    margin-bottom: 1rem;
}

.margin-bottom-1-2 {
    margin-bottom: 1.2rem;
}

.margin-bottom-1-5 {
    margin-bottom: 1.5rem;
}

.margin-bottom-2 {
    margin-bottom: 2rem;
}

.margin-bottom-2-5 {
    margin-bottom: 2.5rem;
}

.margin-bottom-3 {
    margin-bottom: 3rem;
}

.margin-bottom-3-5 {
    margin-bottom: 3.5rem;
}

.margin-bottom-4 {
    margin-bottom: 4rem;
}

.margin-bottom-5 {
    margin-bottom: 5rem;
}

.margin-bottom-6 {
    margin-bottom: 6rem;
}

.margin-bottom-7 {
    margin-bottom: 7rem;
}

.margin-bottom-8 {
    margin-bottom: 8rem;
}

.margin-bottom-9 {
    margin-bottom: 9rem;
}

.margin-bottom-10 {
    margin-bottom: 10rem;
}

@media (min-width: 576px) {
    .margin-bottom-xs-1 {
        margin-bottom: 1rem;
    }

    .margin-bottom-xs-2 {
        margin-bottom: 2rem;
    }

    .margin-bottom-xs-2-5 {
        margin-bottom: 2.5rem;
    }

    .margin-bottom-xs-3 {
        margin-bottom: 3rem;
    }

    .margin-bottom-xs-4 {
        margin-bottom: 4rem;
    }

    .margin-bottom-xs-5 {
        margin-bottom: 5rem;
    }

    .margin-bottom-xs-6 {
        margin-bottom: 6rem;
    }
}

@media (min-width: 771px) {
    .margin-bottom-sm-1 {
        margin-bottom: 1rem;
    }

    .margin-bottom-sm-2 {
        margin-bottom: 2rem;
    }

    .margin-bottom-sm-2-5 {
        margin-bottom: 2.5rem;
    }

    .margin-bottom-sm-3 {
        margin-bottom: 3rem;
    }

    .margin-bottom-sm-4 {
        margin-bottom: 4rem;
    }

    .margin-bottom-sm-5 {
        margin-bottom: 5rem;
    }

    .margin-bottom-sm-6 {
        margin-bottom: 6rem;
    }
}

@media (min-width: 992px) {
    .margin-bottom-md-1 {
        margin-bottom: 1rem;
    }

    .margin-bottom-md-2 {
        margin-bottom: 2rem;
    }

    .margin-bottom-md-3 {
        margin-bottom: 3rem;
    }

    .margin-bottom-md-4 {
        margin-bottom: 4rem;
    }

    .margin-bottom-md-5 {
        margin-bottom: 5rem;
    }

    .margin-bottom-md-6 {
        margin-bottom: 6rem;
    }
}

@media (min-width: 1200px) {
    .margin-bottom-lg-1 {
        margin-bottom: 1rem;
    }

    .margin-bottom-lg-2 {
        margin-bottom: 2rem;
    }

    .margin-bottom-lg-3 {
        margin-bottom: 3rem;
    }

    .margin-bottom-lg-4 {
        margin-bottom: 4rem;
    }

    .margin-bottom-lg-5 {
        margin-bottom: 5rem;
    }

    .margin-bottom-lg-6 {
        margin-bottom: 6rem;
    }
}

@media (min-width: 1440px) {
    .margin-bottom-xl-1 {
        margin-bottom: 1rem;
    }

    .margin-bottom-xl-2 {
        margin-bottom: 2rem;
    }

    .margin-bottom-xl-3 {
        margin-bottom: 3rem;
    }

    .margin-bottom-xl-4 {
        margin-bottom: 4rem;
    }

    .margin-bottom-xl-5 {
        margin-bottom: 5rem;
    }

    .margin-bottom-xl-6 {
        margin-bottom: 6rem;
    }
}

.margin-top-01 {
    margin-top: 0.1rem;
}

.margin-top-02 {
    margin-top: 0.2rem;
}

.margin-top-03 {
    margin-top: 0.3rem;
}

.margin-top-04 {
    margin-top: 0.4rem;
}

.margin-top-05 {
    margin-top: 0.5rem;
}

.margin-top-06 {
    margin-top: 0.6rem;
}

.margin-top-07 {
    margin-top: 0.7rem;
}

.margin-top-08 {
    margin-top: 0.8rem;
}

.margin-top-09 {
    margin-top: 0.9rem;
}

.margin-top-1 {
    margin-top: 1rem;
}

.margin-top-1-5 {
    margin-top: 1.5rem;
}

.margin-top-2 {
    margin-top: 2rem;
}

.margin-top-2-5 {
    margin-top: 2.5rem;
}

.margin-top-3 {
    margin-top: 3rem;
}

.margin-top-3-5 {
    margin-top: 3.5rem;
}

.margin-top-4 {
    margin-top: 4rem;
}

.margin-top-5 {
    margin-top: 5rem;
}

.margin-top-6 {
    margin-top: 6rem;
}

.margin-top-7 {
    margin-top: 7rem;
}

.margin-top-8 {
    margin-top: 8rem;
}

.margin-top-9 {
    margin-top: 9rem;
}

.margin-top-10 {
    margin-top: 10rem;
}

@media (min-width: 576px) {
    .margin-top-xs-1 {
        margin-top: 1rem;
    }

    .margin-top-xs-2 {
        margin-top: 2rem;
    }

    .margin-top-xs-3 {
        margin-top: 3rem;
    }

    .margin-top-xs-4 {
        margin-top: 4rem;
    }

    .margin-top-xs-5 {
        margin-top: 5rem;
    }

    .margin-top-xs-6 {
        margin-top: 6rem;
    }
}

@media (min-width: 771px) {
    .margin-top-sm-1 {
        margin-top: 1rem;
    }

    .margin-top-sm-2 {
        margin-top: 2rem;
    }

    .margin-top-sm-2-5 {
        margin-top: 2.5rem;
    }

    .margin-top-sm-3 {
        margin-top: 3rem;
    }

    .margin-top-sm-4 {
        margin-top: 4rem;
    }

    .margin-top-sm-5 {
        margin-top: 5rem;
    }

    .margin-top-sm-6 {
        margin-top: 6rem;
    }
}

@media (min-width: 992px) {
    .margin-top-md-1 {
        margin-top: 1rem;
    }

    .margin-top-md-2 {
        margin-top: 2rem;
    }

    .margin-top-md-3 {
        margin-top: 3rem;
    }

    .margin-top-md-4 {
        margin-top: 4rem;
    }

    .margin-top-md-5 {
        margin-top: 5rem;
    }

    .margin-top-md-6 {
        margin-top: 6rem;
    }
}

@media (min-width: 1200px) {
    .margin-top-lg-1 {
        margin-top: 1rem;
    }

    .margin-top-lg-2 {
        margin-top: 2rem;
    }

    .margin-top-lg-3 {
        margin-top: 3rem;
    }

    .margin-top-lg-4 {
        margin-top: 4rem;
    }

    .margin-top-lg-5 {
        margin-top: 5rem;
    }

    .margin-top-lg-6 {
        margin-top: 6rem;
    }
}

@media (min-width: 1440px) {
    .margin-top-xl-1 {
        margin-top: 1rem;
    }

    .margin-top-xl-2 {
        margin-top: 2rem;
    }

    .margin-top-xl-3 {
        margin-top: 3rem;
    }

    .margin-top-xl-4 {
        margin-top: 4rem;
    }

    .margin-top-xl-5 {
        margin-top: 5rem;
    }

    .margin-top-xl-6 {
        margin-top: 6rem;
    }
}

.margin-left-01 {
    margin-left: 0.1rem;
}

.margin-left-02 {
    margin-left: 0.2rem;
}

.margin-left-03 {
    margin-left: 0.3rem;
}

.margin-left-04 {
    margin-left: 0.4rem;
}

.margin-left-05 {
    margin-left: 0.5rem;
}

.margin-left-08 {
    margin-left: 0.8rem;
}

.margin-left-1 {
    margin-left: 1rem;
}

.margin-left-1-5 {
    margin-left: 1.5rem;
}

.margin-left-2 {
    margin-left: 2rem;
}

.margin-left-3 {
    margin-left: 3rem;
}

.margin-left-4 {
    margin-left: 4rem;
}

.margin-left-5 {
    margin-left: 5rem;
}

.margin-left-6 {
    margin-left: 6rem;
}

.margin-left-7 {
    margin-left: 7rem;
}

.margin-left-8 {
    margin-left: 8rem;
}

.margin-left-9 {
    margin-left: 9rem;
}

.margin-left-10 {
    margin-left: 10rem;
}

@media (min-width: 576px) {
    .margin-left-xs-1 {
        margin-left: 1rem;
    }

    .margin-left-xs-2 {
        margin-left: 2rem;
    }

    .margin-left-xs-3 {
        margin-left: 3rem;
    }

    .margin-left-xs-4 {
        margin-left: 4rem;
    }

    .margin-left-xs-5 {
        margin-left: 5rem;
    }

    .margin-left-xs-6 {
        margin-left: 6rem;
    }
}

@media (min-width: 771px) {
    .margin-left-sm-1 {
        margin-left: 1rem;
    }

    .margin-left-sm-2 {
        margin-left: 2rem;
    }

    .margin-left-sm-3 {
        margin-left: 3rem;
    }

    .margin-left-sm-4 {
        margin-left: 4rem;
    }

    .margin-left-sm-5 {
        margin-left: 5rem;
    }

    .margin-left-sm-6 {
        margin-left: 6rem;
    }
}

@media (min-width: 992px) {
    .margin-left-md-1 {
        margin-left: 1rem;
    }

    .margin-left-md-2 {
        margin-left: 2rem;
    }

    .margin-left-md-3 {
        margin-left: 3rem;
    }

    .margin-left-md-4 {
        margin-left: 4rem;
    }

    .margin-left-md-5 {
        margin-left: 5rem;
    }

    .margin-left-md-6 {
        margin-left: 6rem;
    }
}

@media (min-width: 1200px) {
    .margin-left-lg-1 {
        margin-left: 1rem;
    }

    .margin-left-lg-2 {
        margin-left: 2rem;
    }

    .margin-left-lg-3 {
        margin-left: 3rem;
    }

    .margin-left-lg-4 {
        margin-left: 4rem;
    }

    .margin-left-lg-5 {
        margin-left: 5rem;
    }

    .margin-left-lg-6 {
        margin-left: 6rem;
    }
}

@media (min-width: 1440px) {
    .margin-left-xl-1 {
        margin-left: 1rem;
    }

    .margin-left-xl-2 {
        margin-left: 2rem;
    }

    .margin-left-xl-3 {
        margin-left: 3rem;
    }

    .margin-left-xl-4 {
        margin-left: 4rem;
    }

    .margin-left-xl-5 {
        margin-left: 5rem;
    }

    .margin-left-xl-6 {
        margin-left: 6rem;
    }
}

.margin-right-01 {
    margin-right: 0.1rem;
}

.margin-right-02 {
    margin-right: 0.2rem;
}

.margin-right-03 {
    margin-right: 0.3rem;
}

.margin-right-04 {
    margin-right: 0.4rem;
}

.margin-right-05 {
    margin-right: 0.5rem;
}

.margin-right-08 {
    margin-right: 0.8rem;
}

.margin-right-1 {
    margin-right: 1rem;
}

.margin-right-1-5 {
    margin-right: 1.5rem;
}

.margin-right-2 {
    margin-right: 2rem;
}

.margin-right-3 {
    margin-right: 3rem;
}

.margin-right-4 {
    margin-right: 4rem;
}

.margin-right-5 {
    margin-right: 5rem;
}

.margin-right-6 {
    margin-right: 6rem;
}

.margin-right-7 {
    margin-right: 7rem;
}

.margin-right-8 {
    margin-right: 8rem;
}

.margin-right-9 {
    margin-right: 9rem;
}

.margin-right-10 {
    margin-right: 10rem;
}

@media (min-width: 576px) {
    .margin-right-xs-1 {
        margin-right: 1rem;
    }

    .margin-right-xs-2 {
        margin-right: 2rem;
    }

    .margin-right-xs-3 {
        margin-right: 3rem;
    }

    .margin-right-xs-4 {
        margin-right: 4rem;
    }

    .margin-right-xs-5 {
        margin-right: 5rem;
    }

    .margin-right-xs-6 {
        margin-right: 6rem;
    }
}

@media (min-width: 771px) {
    .margin-right-sm-1 {
        margin-right: 1rem;
    }

    .margin-right-sm-2 {
        margin-right: 2rem;
    }

    .margin-right-sm-3 {
        margin-right: 3rem;
    }

    .margin-right-sm-4 {
        margin-right: 4rem;
    }

    .margin-right-sm-5 {
        margin-right: 5rem;
    }

    .margin-right-sm-6 {
        margin-right: 6rem;
    }
}

@media (min-width: 992px) {
    .margin-right-md-1 {
        margin-right: 1rem;
    }

    .margin-right-md-2 {
        margin-right: 2rem;
    }

    .margin-right-md-3 {
        margin-right: 3rem;
    }

    .margin-right-md-4 {
        margin-right: 4rem;
    }

    .margin-right-md-5 {
        margin-right: 5rem;
    }

    .margin-right-md-6 {
        margin-right: 6rem;
    }
}

@media (min-width: 1200px) {
    .margin-right-lg-1 {
        margin-right: 1rem;
    }

    .margin-right-lg-2 {
        margin-right: 2rem;
    }

    .margin-right-lg-3 {
        margin-right: 3rem;
    }

    .margin-right-lg-4 {
        margin-right: 4rem;
    }

    .margin-right-lg-5 {
        margin-right: 5rem;
    }

    .margin-right-lg-6 {
        margin-right: 6rem;
    }
}

@media (min-width: 1440px) {
    .margin-right-xl-1 {
        margin-right: 1rem;
    }

    .margin-right-xl-2 {
        margin-right: 2rem;
    }

    .margin-right-xl-3 {
        margin-right: 3rem;
    }

    .margin-right-xl-4 {
        margin-right: 4rem;
    }

    .margin-right-xl-5 {
        margin-right: 5rem;
    }

    .margin-right-xl-6 {
        margin-right: 6rem;
    }
}

.margin-center {
    margin: 0 auto;
}

.margin-left-auto {
    margin-left: auto;
}

.margin-right-auto {
    margin-right: auto;
}

@media (min-width: 576px) {
    .margin-xs-center {
        margin: 0 auto;
    }

    .margin-xs-left-auto {
        margin-left: auto;
    }

    .margin-xs-right-auto {
        margin-right: auto;
    }
}

@media (min-width: 771px) {
    .margin-sm-center {
        margin: 0 auto;
    }

    .margin-sm-left-auto {
        margin-left: auto;
    }

    .margin-sm-right-auto {
        margin-right: auto;
    }
}

@media (min-width: 992px) {
    .margin-md-center {
        margin: 0 auto;
    }

    .margin-md-left-auto {
        margin-left: auto;
    }

    .margin-md-right-auto {
        margin-right: auto;
    }
}

@media (min-width: 1200px) {
    .margin-lg-center {
        margin: 0 auto;
    }

    .margin-lg-left-auto {
        margin-left: auto;
    }

    .margin-lg-right-auto {
        margin-right: auto;
    }
}

.margin-top-0 {
    margin-top: 0 !important;
}

@media (min-width: 576px) {
    .margin-top-xs-0 {
        margin-top: 0 !important;
    }
}

@media (min-width: 771px) {
    .margin-top-sm-0 {
        margin-top: 0 !important;
    }
}

@media (min-width: 992px) {
    .margin-top-md-0 {
        margin-top: 0 !important;
    }
}

@media (min-width: 1200px) {
    .margin-top-lg-0 {
        margin-top: 0 !important;
    }
}

@media (max-width: 1199px) {
    .margin-bp-top-lg-0 {
        margin-top: 0 !important;
    }
}

@media (max-width: 991px) {
    .margin-bp-top-md-0 {
        margin-top: 0 !important;
    }
}

@media (max-width: 770px) {
    .margin-bp-top-sm-0 {
        margin-top: 0 !important;
    }
}

@media (max-width: 575px) {
    .margin-bp-top-xs-0 {
        margin-top: 0 !important;
    }
}

.margin-right-0 {
    margin-right: 0 !important;
}

@media (min-width: 576px) {
    .margin-right-xs-0 {
        margin-right: 0 !important;
    }
}

@media (min-width: 771px) {
    .margin-right-sm-0 {
        margin-right: 0 !important;
    }
}

@media (min-width: 992px) {
    .margin-right-md-0 {
        margin-right: 0 !important;
    }
}

@media (min-width: 1200px) {
    .margin-right-lg-0 {
        margin-right: 0 !important;
    }
}

@media (max-width: 1199px) {
    .margin-bp-right-lg-0 {
        margin-right: 0 !important;
    }
}

@media (max-width: 991px) {
    .margin-bp-right-md-0 {
        margin-right: 0 !important;
    }
}

@media (max-width: 770px) {
    .margin-bp-right-sm-0 {
        margin-right: 0 !important;
    }
}

@media (max-width: 575px) {
    .margin-bp-right-xs-0 {
        margin-right: 0 !important;
    }
}

.margin-bottom-0 {
    margin-bottom: 0 !important;
}

@media (min-width: 576px) {
    .margin-bottom-xs-0 {
        margin-bottom: 0 !important;
    }
}

@media (min-width: 771px) {
    .margin-bottom-sm-0 {
        margin-bottom: 0 !important;
    }
}

@media (min-width: 992px) {
    .margin-bottom-md-0 {
        margin-bottom: 0 !important;
    }
}

@media (min-width: 1200px) {
    .margin-bottom-lg-0 {
        margin-bottom: 0 !important;
    }
}

@media (max-width: 1199px) {
    .margin-bp-bottom-lg-0 {
        margin-bottom: 0 !important;
    }
}

@media (max-width: 991px) {
    .margin-bp-bottom-md-0 {
        margin-bottom: 0 !important;
    }
}

@media (max-width: 770px) {
    .margin-bp-bottom-sm-0 {
        margin-bottom: 0 !important;
    }
}

@media (max-width: 575px) {
    .margin-bp-bottom-xs-0 {
        margin-bottom: 0 !important;
    }
}

.margin-left-0 {
    margin-left: 0 !important;
}

@media (min-width: 576px) {
    .margin-left-xs-0 {
        margin-left: 0 !important;
    }
}

@media (min-width: 771px) {
    .margin-left-sm-0 {
        margin-left: 0 !important;
    }
}

@media (min-width: 992px) {
    .margin-left-md-0 {
        margin-left: 0 !important;
    }
}

@media (min-width: 1200px) {
    .margin-left-lg-0 {
        margin-left: 0 !important;
    }
}

@media (max-width: 1199px) {
    .margin-bp-left-lg-0 {
        margin-left: 0 !important;
    }
}

@media (max-width: 991px) {
    .margin-bp-left-md-0 {
        margin-left: 0 !important;
    }
}

@media (max-width: 770px) {
    .margin-bp-left-sm-0 {
        margin-left: 0 !important;
    }
}

@media (max-width: 575px) {
    .margin-bp-left-xs-0 {
        margin-left: 0 !important;
    }
}

/* Padding block */
.padding-0 {
    padding: 0 !important;
}

@media (min-width: 576px) {
    .padding-xs-0 {
        padding: 0 !important;
    }
}

@media (min-width: 771px) {
    .padding-sm-0 {
        padding: 0 !important;
    }
}

@media (min-width: 992px) {
    .padding-md-0 {
        padding: 0 !important;
    }
}

@media (min-width: 1200px) {
    .padding-lg-0 {
        padding: 0 !important;
    }
}

@media (min-width: 1440px) {
    .padding-xl-0 {
        padding: 0 !important;
    }
}

.padding-top-0 {
    padding-top: 0 !important;
}

.padding-top-xs-0 {
    padding-top: 0 !important;
}

@media (min-width: 771px) {
    .padding-top-sm-0 {
        padding-top: 0 !important;
    }
}

@media (min-width: 992px) {
    .padding-top-md-0 {
        padding-top: 0 !important;
    }
}

@media (min-width: 1200px) {
    .padding-top-lg-0 {
        padding-top: 0 !important;
    }
}

@media (max-width: 1199px) {
    .padding-bp-top-lg-0 {
        padding-top: 0 !important;
    }
}

@media (max-width: 991px) {
    .padding-bp-top-md-0 {
        padding-top: 0 !important;
    }
}

@media (max-width: 770px) {
    .padding-bp-top-sm-0 {
        padding-top: 0 !important;
    }
}

@media (max-width: 575px) {
    .padding-bp-top-xs-0 {
        padding-top: 0 !important;
    }
}

.padding-right-0 {
    padding-right: 0 !important;
}

.padding-right-xs-0 {
    padding-right: 0 !important;
}

@media (min-width: 771px) {
    .padding-right-sm-0 {
        padding-right: 0 !important;
    }
}

@media (min-width: 992px) {
    .padding-right-md-0 {
        padding-right: 0 !important;
    }
}

@media (min-width: 1200px) {
    .padding-right-lg-0 {
        padding-right: 0 !important;
    }
}

@media (max-width: 1199px) {
    .padding-bp-right-lg-0 {
        padding-right: 0 !important;
    }
}

@media (max-width: 991px) {
    .padding-bp-right-md-0 {
        padding-right: 0 !important;
    }
}

@media (max-width: 770px) {
    .padding-bp-right-sm-0 {
        padding-right: 0 !important;
    }
}

@media (max-width: 575px) {
    .padding-bp-right-xs-0 {
        padding-right: 0 !important;
    }
}

.padding-bottom-0 {
    padding-bottom: 0 !important;
}

.padding-bottom-xs-0 {
    padding-bottom: 0 !important;
}

@media (min-width: 771px) {
    .padding-bottom-sm-0 {
        padding-bottom: 0 !important;
    }
}

@media (min-width: 992px) {
    .padding-bottom-md-0 {
        padding-bottom: 0 !important;
    }
}

@media (min-width: 1200px) {
    .padding-bottom-lg-0 {
        padding-bottom: 0 !important;
    }
}

@media (max-width: 1199px) {
    .padding-bp-bottom-lg-0 {
        padding-bottom: 0 !important;
    }
}

@media (max-width: 991px) {
    .padding-bp-bottom-md-0 {
        padding-bottom: 0 !important;
    }
}

@media (max-width: 770px) {
    .padding-bp-bottom-sm-0 {
        padding-bottom: 0 !important;
    }
}

@media (max-width: 575px) {
    .padding-bp-bottom-xs-0 {
        padding-bottom: 0 !important;
    }
}

.padding-left-0 {
    padding-left: 0 !important;
}

.padding-left-xs-0 {
    padding-left: 0 !important;
}

@media (min-width: 771px) {
    .padding-left-sm-0 {
        padding-left: 0 !important;
    }
}

@media (min-width: 992px) {
    .padding-left-md-0 {
        padding-left: 0 !important;
    }
}

@media (min-width: 1200px) {
    .padding-left-lg-0 {
        padding-left: 0 !important;
    }
}

@media (max-width: 1199px) {
    .padding-bp-left-lg-0 {
        padding-left: 0 !important;
    }
}

@media (max-width: 991px) {
    .padding-bp-left-md-0 {
        padding-left: 0 !important;
    }
}

@media (max-width: 770px) {
    .padding-bp-left-sm-0 {
        padding-left: 0 !important;
    }
}

@media (max-width: 575px) {
    .padding-bp-left-xs-0 {
        padding-left: 0 !important;
    }
}

/* Position helpers */
.pos-r {
    position: relative;
}

.pos-f {
    position: fixed;
}

.pos-s {
    position: static;
}

.pos-sticky {
    position: -webkit-sticky;
    position: sticky;
}

.pos-a {
    position: absolute;
}

.pos-auto {
    position: inherit;
}

/* Size helpers */
.w-100 {
    width: 100%;
}

.w-50 {
    width: 50%;
}

.w-25 {
    width: 25%;
}

.h-vh-100 {
    height: 100vh;
}

.h-100 {
    height: 100%;
}

.h-50 {
    height: 50%;
}

.h-25 {
    height: 25%;
}

.h-20 {
    height: 20px;
}

/* Text helpers */
.txt-r {
    text-align: right;
}

.txt-c {
    text-align: center;
}

.txt-l {
    text-align: left;
}

@media (min-width: 576px) {
    .txt-xs-c {
        text-align: center;
    }

    .txt-xs-l {
        text-align: left;
    }

    .txt-xs-r {
        text-align: right;
    }
}

@media (min-width: 771px) {
    .txt-sm-l {
        text-align: left;
    }

    .txt-sm-r {
        text-align: right;
    }

    .txt-sm-c {
        text-align: center;
    }
}

@media (min-width: 992px) {
    .txt-md-l {
        text-align: left;
    }

    .txt-md-r {
        text-align: right;
    }

    .txt-md-c {
        text-align: center;
    }
}

@media (min-width: 1200px) {
    .txt-lg-r {
        text-align: right;
    }

    .txt-lg-l {
        text-align: left;
    }

    .txt-lg-c {
        text-align: center;
    }
}

.txt-upper {
    text-transform: uppercase;
}

.txt-lower {
    text-transform: lowercase;
}

.txt-italic {
    font-style: italic;
}

.txt-300 {
    font-weight: 300;
}

.txt-400 {
    font-weight: 400;
}

.txt-700 {
    font-weight: 700;
}

.txt-line {
    text-decoration: line-through;
}

.txt-underline {
    text-decoration: underline;
}

/* Z-index helpers */
.z-index--1 {
    z-index: -1;
}

.z-index-1 {
    z-index: 1;
}

.z-index-2 {
    z-index: 2;
}

.z-index-3 {
    z-index: 3;
}

.z-index-4 {
    z-index: 4;
}

.z-index-5 {
    z-index: 5;
}

.z-index-6 {
    z-index: 6;
}

.z-index-7 {
    z-index: 7;
}

.z-index-8 {
    z-index: 8;
}

.z-index-9 {
    z-index: 9;
}

.z-index-10 {
    z-index: 10;
}

.z-index-100 {
    z-index: 100;
}

.z-index-1000 {
    z-index: 1000;
}

.z-index-10000 {
    z-index: 10000;
}

.z-index-100000 {
    z-index: 100000;
}

/* Opacity helpers */
.opacity-0 {
    opacity: 0;
}

.opacity-01 {
    opacity: 0.1;
}

.opacity-02 {
    opacity: 0.2;
}

.opacity-03 {
    opacity: 0.3;
}

.opacity-04 {
    opacity: 0.4;
}

.opacity-05 {
    opacity: 0.5;
}

.opacity-06 {
    opacity: 0.6;
}

.opacity-07 {
    opacity: 0.7;
}

.opacity-08 {
    opacity: 0.8;
}

.opacity-09 {
    opacity: 0.9;
}

.opacity-1 {
    opacity: 1;
}

/* Dots helpers */
.clip-text {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
}

.clip-text--2 {
    -webkit-line-clamp: 2;
}

.clip-text--3 {
    -webkit-line-clamp: 3;
}

.clip-text--4 {
    -webkit-line-clamp: 4;
}

.dots {
    overflow: hidden;
}

/* IE fallback */
.no-object-fit .img-flex-cover img {
    height: auto;
    width: auto;
    vertical-align: middle;
    margin: 0 auto;
}

.no-object-fit .img-flex-contain {
    display: block;
    height: auto;
}

.no-object-fit .img-flex-contain img {
    height: 100%;
    width: auto;
    vertical-align: middle;
    margin: 0 auto;
}

/* Base settings */
*,
*::before,
*::after {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

* {
    -webkit-transition: color 0.15s, background 0.15s, border 0.15s, opacity 0.15s;
    -o-transition: color 0.15s, background 0.15s, border 0.15s, opacity 0.15s;
    transition: color 0.15s, background 0.15s, border 0.15s, opacity 0.15s;
    word-break: break-word;
}

body header {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
}

*:after,
*:before {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    outline: none;
}

::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    border-radius: 0;
    background: #f7f7f7;
}

::-webkit-scrollbar-thumb {
    border-radius: 0;
    background: #000000;
}

::-webkit-scrollbar-thumb:hover {
    background: black;
}

@-ms-viewport {
    width: device-width;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
    display: block;
}

html {
    position: relative;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    height: 100%;
    font-size: 100%;
}

body {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    height: 100%;
    min-width: 310px;
    margin: 0;
    color: #000;
    font-family: "SetupGrotesk-Regular", Arial, sans-serif;
    font-weight: 400;
    font-size: 1rem;
    line-height: 1.5;
}

em,
i,
.italic {
    font-style: italic;
}

strong,
.strong {
    font-weight: 700;
}

small,
.small {
    font-size: 12px;
    font-size: 0.75rem;
}

h1,
.alfa,
h2,
.beta,
h3,
.gamma,
h4,
.delta,
h5,
.epsilon,
h6,
.zeta {
    margin: 0;
    margin-bottom: 1.5rem;
    color: #000000;
    font-family: "SetupGrotesk-Bold", Arial, sans-serif;
    font-weight: 700;
    line-height: 1.2;
    -webkit-transition: font-size 0.3s cubic-bezier(0.16, 0.08, 0.355, 1);
    -o-transition: font-size 0.3s cubic-bezier(0.16, 0.08, 0.355, 1);
    transition: font-size 0.3s cubic-bezier(0.16, 0.08, 0.355, 1);
}

h1 a,
.alfa a,
h2 a,
.beta a,
h3 a,
.gamma a,
h4 a,
.delta a,
h5 a,
.epsilon a,
h6 a,
.zeta a {
    font-family: inherit;
    color: inherit;
}

h1,
.alfa {
    font-size: 24px;
    font-size: 1.5rem;
}

@media (min-width: 576px) {
    h1,
    .alfa {
        font-size: 28px;
        font-size: 1.75rem;
    }
}

@media (min-width: 771px) {
    h1,
    .alfa {
        font-size: 28px;
        font-size: 1.75rem;
    }
}

@media (min-width: 771px) {
    .alfa-big {
        font-size: 32px;
        font-size: 2rem;
    }
}

h2,
.beta {
    font-size: 18px;
    font-size: 1.125rem;
}

@media (min-width: 576px) {
    h2,
    .beta {
        font-size: 24px;
        font-size: 1.5rem;
    }
}

@media (min-width: 771px) {
    h2,
    .beta {
        font-size: 24px;
        font-size: 1.5rem;
    }
}

h3,
.gamma {
    font-size: 18px;
    font-size: 1.125rem;
}

@media (min-width: 576px) {
    h3,
    .gamma {
        font-size: 22px;
        font-size: 1.375rem;
    }
}

@media (min-width: 771px) {
    h3,
    .gamma {
        font-size: 22px;
        font-size: 1.375rem;
    }
}

h4,
.delta {
    font-size: 28px;
    font-size: 1.75rem;
}

@media (min-width: 576px) {
    h4,
    .delta {
        font-size: 30px;
        font-size: 1.875rem;
    }
}

@media (min-width: 771px) {
    h4,
    .delta {
        font-size: 32px;
        font-size: 2rem;
    }
}

h5,
.epsilon {
    font-size: 24px;
    font-size: 1.5rem;
}

@media (min-width: 576px) {
    h5,
    .epsilon {
        font-size: 26px;
        font-size: 1.625rem;
    }
}

@media (min-width: 771px) {
    h5,
    .epsilon {
        font-size: 28px;
        font-size: 1.75rem;
    }
}

h6,
.zeta {
    font-size: 22px;
    font-size: 1.375rem;
}

@media (min-width: 576px) {
    h6,
    .zeta {
        font-size: 24px;
        font-size: 1.5rem;
    }
}

@media (min-width: 771px) {
    h6,
    .zeta {
        font-size: 26px;
        font-size: 1.625rem;
    }
}

.h--font-light h1,
.h--font-light .alfa,
.h--font-light h2,
.h--font-light .beta,
.h--font-light h3,
.h--font-light .gamma,
.h--font-light h4,
.h--font-light .delta,
.h--font-light h5,
.h--font-light .epsilon,
.h--font-light h6,
.h--font-light .zeta {
    font-weight: 300;
}

.h--font-regular h1,
.h--font-regular .alfa,
.h--font-regular h2,
.h--font-regular .beta,
.h--font-regular h3,
.h--font-regular .gamma,
.h--font-regular h4,
.h--font-regular .delta,
.h--font-regular h5,
.h--font-regular .epsilon,
.h--font-regular h6,
.h--font-regular .zeta {
    font-family: "SetupGrotesk-Regular", Arial, sans-serif;
    font-weight: 400;
}

.h--font-regular h1 a,
.h--font-regular .alfa a,
.h--font-regular h2 a,
.h--font-regular .beta a,
.h--font-regular h3 a,
.h--font-regular .gamma a,
.h--font-regular h4 a,
.h--font-regular .delta a,
.h--font-regular h5 a,
.h--font-regular .epsilon a,
.h--font-regular h6 a,
.h--font-regular .zeta a {
    font-family: "SetupGrotesk-Regular", Arial, sans-serif;
    font-weight: 400;
}

.h--font-medium h1,
.h--font-medium .alfa,
.h--font-medium h2,
.h--font-medium .beta,
.h--font-medium h3,
.h--font-medium .gamma,
.h--font-medium h4,
.h--font-medium .delta,
.h--font-medium h5,
.h--font-medium .epsilon,
.h--font-medium h6,
.h--font-medium .zeta {
    font-family: "SetupGrotesk-Medium", Arial, sans-serif;
    font-weight: 500;
}

.h--font-medium h1 a,
.h--font-medium .alfa a,
.h--font-medium h2 a,
.h--font-medium .beta a,
.h--font-medium h3 a,
.h--font-medium .gamma a,
.h--font-medium h4 a,
.h--font-medium .delta a,
.h--font-medium h5 a,
.h--font-medium .epsilon a,
.h--font-medium h6 a,
.h--font-medium .zeta a {
    font-family: "SetupGrotesk-Medium", Arial, sans-serif;
    font-weight: 500;
}

@media (max-width: 575px) {
    .h--xs-font-primary-regular h1,
    .h--xs-font-primary-regular .alfa,
    .h--xs-font-primary-regular h2,
    .h--xs-font-primary-regular .beta,
    .h--xs-font-primary-regular h3,
    .h--xs-font-primary-regular .gamma,
    .h--xs-font-primary-regular h4,
    .h--xs-font-primary-regular .delta,
    .h--xs-font-primary-regular h5,
    .h--xs-font-primary-regular .epsilon,
    .h--xs-font-primary-regular h6,
    .h--xs-font-primary-regular .zeta {
        font-family: "SetupGrotesk-Regular", Arial, sans-serif;
        font-weight: 400;
    }

    .h--xs-font-primary-regular h1 a,
    .h--xs-font-primary-regular .alfa a,
    .h--xs-font-primary-regular h2 a,
    .h--xs-font-primary-regular .beta a,
    .h--xs-font-primary-regular h3 a,
    .h--xs-font-primary-regular .gamma a,
    .h--xs-font-primary-regular h4 a,
    .h--xs-font-primary-regular .delta a,
    .h--xs-font-primary-regular h5 a,
    .h--xs-font-primary-regular .epsilon a,
    .h--xs-font-primary-regular h6 a,
    .h--xs-font-primary-regular .zeta a {
        font-family: "SetupGrotesk-Regular", Arial, sans-serif;
        font-weight: 400;
    }
}

.h--font-semibold h1,
.h--font-semibold .alfa,
.h--font-semibold h2,
.h--font-semibold .beta,
.h--font-semibold h3,
.h--font-semibold .gamma,
.h--font-semibold h4,
.h--font-semibold .delta,
.h--font-semibold h5,
.h--font-semibold .epsilon,
.h--font-semibold h6,
.h--font-semibold .zeta {
    font-weight: 600;
}

.h--font-bold h1,
.h--font-bold .alfa,
.h--font-bold h2,
.h--font-bold .beta,
.h--font-bold h3,
.h--font-bold .gamma,
.h--font-bold h4,
.h--font-bold .delta,
.h--font-bold h5,
.h--font-bold .epsilon,
.h--font-bold h6,
.h--font-bold .zeta {
    font-weight: 700;
}

.h--font-black h1,
.h--font-black .alfa,
.h--font-black h2,
.h--font-black .beta,
.h--font-black h3,
.h--font-black .gamma,
.h--font-black h4,
.h--font-black .delta,
.h--font-black h5,
.h--font-black .epsilon,
.h--font-black h6,
.h--font-black .zeta {
    font-weight: 900;
}

@media (min-width: 576px) {
    .h--font-xs-regular h1,
    .h--font-xs-regular .alfa,
    .h--font-xs-regular h2,
    .h--font-xs-regular .beta,
    .h--font-xs-regular h3,
    .h--font-xs-regular .gamma,
    .h--font-xs-regular h4,
    .h--font-xs-regular .delta,
    .h--font-xs-regular h5,
    .h--font-xs-regular .epsilon,
    .h--font-xs-regular h6,
    .h--font-xs-regular .zeta {
        font-weight: 400;
    }
}

.h--font-primary-regular h1,
.h--font-primary-regular .alfa,
.h--font-primary-regular h2,
.h--font-primary-regular .beta,
.h--font-primary-regular h3,
.h--font-primary-regular .gamma,
.h--font-primary-regular h4,
.h--font-primary-regular .delta,
.h--font-primary-regular h5,
.h--font-primary-regular .epsilon,
.h--font-primary-regular h6,
.h--font-primary-regular .zeta {
    font-family: "SetupGrotesk-Regular", Arial, sans-serif;
}

.h--font-primary-bold h1,
.h--font-primary-bold .alfa,
.h--font-primary-bold h2,
.h--font-primary-bold .beta,
.h--font-primary-bold h3,
.h--font-primary-bold .gamma,
.h--font-primary-bold h4,
.h--font-primary-bold .delta,
.h--font-primary-bold h5,
.h--font-primary-bold .epsilon,
.h--font-primary-bold h6,
.h--font-primary-bold .zeta {
    font-family: "SetupGrotesk-Bold", Arial, sans-serif;
}

.h--text-upper h1,
.h--text-upper .alfa,
.h--text-upper h2,
.h--text-upper .beta,
.h--text-upper h3,
.h--text-upper .gamma,
.h--text-upper h4,
.h--text-upper .delta,
.h--text-upper h5,
.h--text-upper .epsilon,
.h--text-upper h6,
.h--text-upper .zeta {
    text-transform: uppercase;
}

.h--line-08 h1,
.h--line-08 .alfa,
.h--line-08 h2,
.h--line-08 .beta,
.h--line-08 h3,
.h--line-08 .gamma,
.h--line-08 h4,
.h--line-08 .delta,
.h--line-08 h5,
.h--line-08 .epsilon,
.h--line-08 h6,
.h--line-08 .zeta {
    line-height: 0.8;
}

.h--line-1 h1,
.h--line-1 .alfa,
.h--line-1 h2,
.h--line-1 .beta,
.h--line-1 h3,
.h--line-1 .gamma,
.h--line-1 h4,
.h--line-1 .delta,
.h--line-1 h5,
.h--line-1 .epsilon,
.h--line-1 h6,
.h--line-1 .zeta {
    line-height: 1;
}

.h--line-1-2 h1,
.h--line-1-2 .alfa,
.h--line-1-2 h2,
.h--line-1-2 .beta,
.h--line-1-2 h3,
.h--line-1-2 .gamma,
.h--line-1-2 h4,
.h--line-1-2 .delta,
.h--line-1-2 h5,
.h--line-1-2 .epsilon,
.h--line-1-2 h6,
.h--line-1-2 .zeta {
    line-height: 1.2;
}

.h--line-1-4 h1,
.h--line-1-4 .alfa,
.h--line-1-4 h2,
.h--line-1-4 .beta,
.h--line-1-4 h3,
.h--line-1-4 .gamma,
.h--line-1-4 h4,
.h--line-1-4 .delta,
.h--line-1-4 h5,
.h--line-1-4 .epsilon,
.h--line-1-4 h6,
.h--line-1-4 .zeta {
    line-height: 1.4;
}

.h--line-1-5 h1,
.h--line-1-5 .alfa,
.h--line-1-5 h2,
.h--line-1-5 .beta,
.h--line-1-5 h3,
.h--line-1-5 .gamma,
.h--line-1-5 h4,
.h--line-1-5 .delta,
.h--line-1-5 h5,
.h--line-1-5 .epsilon,
.h--line-1-5 h6,
.h--line-1-5 .zeta {
    line-height: 1.5;
}

.h--line-1-6 h1,
.h--line-1-6 .alfa,
.h--line-1-6 h2,
.h--line-1-6 .beta,
.h--line-1-6 h3,
.h--line-1-6 .gamma,
.h--line-1-6 h4,
.h--line-1-6 .delta,
.h--line-1-6 h5,
.h--line-1-6 .epsilon,
.h--line-1-6 h6,
.h--line-1-6 .zeta {
    line-height: 1.6;
}

.h--line-1-8 h1,
.h--line-1-8 .alfa,
.h--line-1-8 h2,
.h--line-1-8 .beta,
.h--line-1-8 h3,
.h--line-1-8 .gamma,
.h--line-1-8 h4,
.h--line-1-8 .delta,
.h--line-1-8 h5,
.h--line-1-8 .epsilon,
.h--line-1-8 h6,
.h--line-1-8 .zeta {
    line-height: 1.8;
}

.h--line-2 h1,
.h--line-2 .alfa,
.h--line-2 h2,
.h--line-2 .beta,
.h--line-2 h3,
.h--line-2 .gamma,
.h--line-2 h4,
.h--line-2 .delta,
.h--line-2 h5,
.h--line-2 .epsilon,
.h--line-2 h6,
.h--line-2 .zeta {
    line-height: 2;
}

.h--line-2-2 h1,
.h--line-2-2 .alfa,
.h--line-2-2 h2,
.h--line-2-2 .beta,
.h--line-2-2 h3,
.h--line-2-2 .gamma,
.h--line-2-2 h4,
.h--line-2-2 .delta,
.h--line-2-2 h5,
.h--line-2-2 .epsilon,
.h--line-2-2 h6,
.h--line-2-2 .zeta {
    line-height: 2.2;
}

.h--line-2-5 h1,
.h--line-2-5 .alfa,
.h--line-2-5 h2,
.h--line-2-5 .beta,
.h--line-2-5 h3,
.h--line-2-5 .gamma,
.h--line-2-5 h4,
.h--line-2-5 .delta,
.h--line-2-5 h5,
.h--line-2-5 .epsilon,
.h--line-2-5 h6,
.h--line-2-5 .zeta {
    line-height: 2.5;
}

@media (min-width: 576px) {
    .h--line-xs-1 h1,
    .h--line-xs-1 .alfa,
    .h--line-xs-1 h2,
    .h--line-xs-1 .beta,
    .h--line-xs-1 h3,
    .h--line-xs-1 .gamma,
    .h--line-xs-1 h4,
    .h--line-xs-1 .delta,
    .h--line-xs-1 h5,
    .h--line-xs-1 .epsilon,
    .h--line-xs-1 h6,
    .h--line-xs-1 .zeta {
        line-height: 1;
    }

    .h--line-xs-1-2 h1,
    .h--line-xs-1-2 .alfa,
    .h--line-xs-1-2 h2,
    .h--line-xs-1-2 .beta,
    .h--line-xs-1-2 h3,
    .h--line-xs-1-2 .gamma,
    .h--line-xs-1-2 h4,
    .h--line-xs-1-2 .delta,
    .h--line-xs-1-2 h5,
    .h--line-xs-1-2 .epsilon,
    .h--line-xs-1-2 h6,
    .h--line-xs-1-2 .zeta {
        line-height: 1.2;
    }

    .h--line-xs-1-4 h1,
    .h--line-xs-1-4 .alfa,
    .h--line-xs-1-4 h2,
    .h--line-xs-1-4 .beta,
    .h--line-xs-1-4 h3,
    .h--line-xs-1-4 .gamma,
    .h--line-xs-1-4 h4,
    .h--line-xs-1-4 .delta,
    .h--line-xs-1-4 h5,
    .h--line-xs-1-4 .epsilon,
    .h--line-xs-1-4 h6,
    .h--line-xs-1-4 .zeta {
        line-height: 1.4;
    }

    .h--line-xs-1-6 h1,
    .h--line-xs-1-6 .alfa,
    .h--line-xs-1-6 h2,
    .h--line-xs-1-6 .beta,
    .h--line-xs-1-6 h3,
    .h--line-xs-1-6 .gamma,
    .h--line-xs-1-6 h4,
    .h--line-xs-1-6 .delta,
    .h--line-xs-1-6 h5,
    .h--line-xs-1-6 .epsilon,
    .h--line-xs-1-6 h6,
    .h--line-xs-1-6 .zeta {
        line-height: 1.6;
    }
}

@media (min-width: 771px) {
    .h--line-sm-1 h1,
    .h--line-sm-1 .alfa,
    .h--line-sm-1 h2,
    .h--line-sm-1 .beta,
    .h--line-sm-1 h3,
    .h--line-sm-1 .gamma,
    .h--line-sm-1 h4,
    .h--line-sm-1 .delta,
    .h--line-sm-1 h5,
    .h--line-sm-1 .epsilon,
    .h--line-sm-1 h6,
    .h--line-sm-1 .zeta {
        line-height: 1;
    }

    .h--line-sm-1-2 h1,
    .h--line-sm-1-2 .alfa,
    .h--line-sm-1-2 h2,
    .h--line-sm-1-2 .beta,
    .h--line-sm-1-2 h3,
    .h--line-sm-1-2 .gamma,
    .h--line-sm-1-2 h4,
    .h--line-sm-1-2 .delta,
    .h--line-sm-1-2 h5,
    .h--line-sm-1-2 .epsilon,
    .h--line-sm-1-2 h6,
    .h--line-sm-1-2 .zeta {
        line-height: 1.2;
    }

    .h--line-sm-1-4 h1,
    .h--line-sm-1-4 .alfa,
    .h--line-sm-1-4 h2,
    .h--line-sm-1-4 .beta,
    .h--line-sm-1-4 h3,
    .h--line-sm-1-4 .gamma,
    .h--line-sm-1-4 h4,
    .h--line-sm-1-4 .delta,
    .h--line-sm-1-4 h5,
    .h--line-sm-1-4 .epsilon,
    .h--line-sm-1-4 h6,
    .h--line-sm-1-4 .zeta {
        line-height: 1.4;
    }

    .h--line-sm-1-6 h1,
    .h--line-sm-1-6 .alfa,
    .h--line-sm-1-6 h2,
    .h--line-sm-1-6 .beta,
    .h--line-sm-1-6 h3,
    .h--line-sm-1-6 .gamma,
    .h--line-sm-1-6 h4,
    .h--line-sm-1-6 .delta,
    .h--line-sm-1-6 h5,
    .h--line-sm-1-6 .epsilon,
    .h--line-sm-1-6 h6,
    .h--line-sm-1-6 .zeta {
        line-height: 1.6;
    }
}

@media (min-width: 992px) {
    .h--line-md-1 h1,
    .h--line-md-1 .alfa,
    .h--line-md-1 h2,
    .h--line-md-1 .beta,
    .h--line-md-1 h3,
    .h--line-md-1 .gamma,
    .h--line-md-1 h4,
    .h--line-md-1 .delta,
    .h--line-md-1 h5,
    .h--line-md-1 .epsilon,
    .h--line-md-1 h6,
    .h--line-md-1 .zeta {
        line-height: 1;
    }

    .h--line-md-1-2 h1,
    .h--line-md-1-2 .alfa,
    .h--line-md-1-2 h2,
    .h--line-md-1-2 .beta,
    .h--line-md-1-2 h3,
    .h--line-md-1-2 .gamma,
    .h--line-md-1-2 h4,
    .h--line-md-1-2 .delta,
    .h--line-md-1-2 h5,
    .h--line-md-1-2 .epsilon,
    .h--line-md-1-2 h6,
    .h--line-md-1-2 .zeta {
        line-height: 1.2;
    }

    .h--line-md-1-4 h1,
    .h--line-md-1-4 .alfa,
    .h--line-md-1-4 h2,
    .h--line-md-1-4 .beta,
    .h--line-md-1-4 h3,
    .h--line-md-1-4 .gamma,
    .h--line-md-1-4 h4,
    .h--line-md-1-4 .delta,
    .h--line-md-1-4 h5,
    .h--line-md-1-4 .epsilon,
    .h--line-md-1-4 h6,
    .h--line-md-1-4 .zeta {
        line-height: 1.4;
    }

    .h--line-md-1-6 h1,
    .h--line-md-1-6 .alfa,
    .h--line-md-1-6 h2,
    .h--line-md-1-6 .beta,
    .h--line-md-1-6 h3,
    .h--line-md-1-6 .gamma,
    .h--line-md-1-6 h4,
    .h--line-md-1-6 .delta,
    .h--line-md-1-6 h5,
    .h--line-md-1-6 .epsilon,
    .h--line-md-1-6 h6,
    .h--line-md-1-6 .zeta {
        line-height: 1.6;
    }
}

.h--margin-0 .alfa,
.h--margin-0 .beta,
.h--margin-0 .gamma,
.h--margin-0 .delta,
.h--margin-0 .epsilon,
.h--margin-0 .zeta,
.h--margin-0 h1,
.h--margin-0 h2,
.h--margin-0 h3,
.h--margin-0 h4,
.h--margin-0 h5,
.h--margin-0 h6 {
    margin-bottom: 0;
}

.h--margin-01 .alfa,
.h--margin-01 .beta,
.h--margin-01 .gamma,
.h--margin-01 .delta,
.h--margin-01 .epsilon,
.h--margin-01 .zeta,
.h--margin-01 h1,
.h--margin-01 h2,
.h--margin-01 h3,
.h--margin-01 h4,
.h--margin-01 h5,
.h--margin-01 h6 {
    margin-bottom: 0.1rem;
}

.h--margin-02 .alfa,
.h--margin-02 .beta,
.h--margin-02 .gamma,
.h--margin-02 .delta,
.h--margin-02 .epsilon,
.h--margin-02 .zeta,
.h--margin-02 h1,
.h--margin-02 h2,
.h--margin-02 h3,
.h--margin-02 h4,
.h--margin-02 h5,
.h--margin-02 h6 {
    margin-bottom: 0.2rem;
}

.h--margin-03 .alfa,
.h--margin-03 .beta,
.h--margin-03 .gamma,
.h--margin-03 .delta,
.h--margin-03 .epsilon,
.h--margin-03 .zeta,
.h--margin-03 h1,
.h--margin-03 h2,
.h--margin-03 h3,
.h--margin-03 h4,
.h--margin-03 h5,
.h--margin-03 h6 {
    margin-bottom: 0.3rem;
}

.h--margin-04 .alfa,
.h--margin-04 .beta,
.h--margin-04 .gamma,
.h--margin-04 .delta,
.h--margin-04 .epsilon,
.h--margin-04 .zeta,
.h--margin-04 h1,
.h--margin-04 h2,
.h--margin-04 h3,
.h--margin-04 h4,
.h--margin-04 h5,
.h--margin-04 h6 {
    margin-bottom: 0.4rem;
}

.h--margin-05 .alfa,
.h--margin-05 .beta,
.h--margin-05 .gamma,
.h--margin-05 .delta,
.h--margin-05 .epsilon,
.h--margin-05 .zeta,
.h--margin-05 h1,
.h--margin-05 h2,
.h--margin-05 h3,
.h--margin-05 h4,
.h--margin-05 h5,
.h--margin-05 h6 {
    margin-bottom: 0.5rem;
}

.h--margin-06 .alfa,
.h--margin-06 .beta,
.h--margin-06 .gamma,
.h--margin-06 .delta,
.h--margin-06 .epsilon,
.h--margin-06 .zeta,
.h--margin-06 h1,
.h--margin-06 h2,
.h--margin-06 h3,
.h--margin-06 h4,
.h--margin-06 h5,
.h--margin-06 h6 {
    margin-bottom: 0.6rem;
}

.h--margin-07 .alfa,
.h--margin-07 .beta,
.h--margin-07 .gamma,
.h--margin-07 .delta,
.h--margin-07 .epsilon,
.h--margin-07 .zeta,
.h--margin-07 h1,
.h--margin-07 h2,
.h--margin-07 h3,
.h--margin-07 h4,
.h--margin-07 h5,
.h--margin-07 h6 {
    margin-bottom: 0.7rem;
}

.h--margin-08 .alfa,
.h--margin-08 .beta,
.h--margin-08 .gamma,
.h--margin-08 .delta,
.h--margin-08 .epsilon,
.h--margin-08 .zeta,
.h--margin-08 h1,
.h--margin-08 h2,
.h--margin-08 h3,
.h--margin-08 h4,
.h--margin-08 h5,
.h--margin-08 h6 {
    margin-bottom: 0.8rem;
}

.h--margin-09 .alfa,
.h--margin-09 .beta,
.h--margin-09 .gamma,
.h--margin-09 .delta,
.h--margin-09 .epsilon,
.h--margin-09 .zeta,
.h--margin-09 h1,
.h--margin-09 h2,
.h--margin-09 h3,
.h--margin-09 h4,
.h--margin-09 h5,
.h--margin-09 h6 {
    margin-bottom: 0.9rem;
}

.h--margin-1 .alfa,
.h--margin-1 .beta,
.h--margin-1 .gamma,
.h--margin-1 .delta,
.h--margin-1 .epsilon,
.h--margin-1 .zeta,
.h--margin-1 h1,
.h--margin-1 h2,
.h--margin-1 h3,
.h--margin-1 h4,
.h--margin-1 h5,
.h--margin-1 h6 {
    margin-bottom: 1rem;
}

.h--margin-1-2 .alfa,
.h--margin-1-2 .beta,
.h--margin-1-2 .gamma,
.h--margin-1-2 .delta,
.h--margin-1-2 .epsilon,
.h--margin-1-2 .zeta,
.h--margin-1-2 h1,
.h--margin-1-2 h2,
.h--margin-1-2 h3,
.h--margin-1-2 h4,
.h--margin-1-2 h5,
.h--margin-1-2 h6 {
    margin-bottom: 1.2rem;
}

.h--margin-1-5 .alfa,
.h--margin-1-5 .beta,
.h--margin-1-5 .gamma,
.h--margin-1-5 .delta,
.h--margin-1-5 .epsilon,
.h--margin-1-5 .zeta,
.h--margin-1-5 h1,
.h--margin-1-5 h2,
.h--margin-1-5 h3,
.h--margin-1-5 h4,
.h--margin-1-5 h5,
.h--margin-1-5 h6 {
    margin-bottom: 1.5rem;
}

.h--margin-2 .alfa,
.h--margin-2 .beta,
.h--margin-2 .gamma,
.h--margin-2 .delta,
.h--margin-2 .epsilon,
.h--margin-2 .zeta,
.h--margin-2 h1,
.h--margin-2 h2,
.h--margin-2 h3,
.h--margin-2 h4,
.h--margin-2 h5,
.h--margin-2 h6 {
    margin-bottom: 2rem;
}

.h--margin-2-5 .alfa,
.h--margin-2-5 .beta,
.h--margin-2-5 .gamma,
.h--margin-2-5 .delta,
.h--margin-2-5 .epsilon,
.h--margin-2-5 .zeta,
.h--margin-2-5 h1,
.h--margin-2-5 h2,
.h--margin-2-5 h3,
.h--margin-2-5 h4,
.h--margin-2-5 h5,
.h--margin-2-5 h6 {
    margin-bottom: 2.5rem;
}

.h--margin-3 .alfa,
.h--margin-3 .beta,
.h--margin-3 .gamma,
.h--margin-3 .delta,
.h--margin-3 .epsilon,
.h--margin-3 .zeta,
.h--margin-3 h1,
.h--margin-3 h2,
.h--margin-3 h3,
.h--margin-3 h4,
.h--margin-3 h5,
.h--margin-3 h6 {
    margin-bottom: 3rem;
}

a {
    cursor: pointer;
    text-decoration: none;
    color: inherit;
    outline: none;
    font-family: "SetupGrotesk-Regular", Arial, sans-serif;
}

a:focus,
a:active,
a:visited {
    outline: none;
}

.a-hover:hover, .a-hover:focus {
    text-decoration: underline;
}

.a-link {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    cursor: pointer;
    padding: 0.6rem 0;
}

.a-link__text {
    color: #000000;
    font-size: 16px;
    font-size: 1rem;
    text-decoration: underline;
}

.a-link__text--black {
    color: #000000;
}

.a-link__text--white {
    color: #ffffff;
}

.a-link__text--purple {
    color: #000000;
}

.a-link__text--size-12 {
    font-size: 12px;
    font-size: 0.75rem;
}

.a-link__text--size-14 {
    font-size: 14px;
    font-size: 0.875rem;
}

.a-link__text--size-16 {
    font-size: 16px;
    font-size: 1rem;
}

.a-link__text--size-18 {
    font-size: 18px;
    font-size: 1.125rem;
}

.a-link__icon {
    display: inline-block;
    vertical-align: middle;
}

.a-link--hover-red {
    position: relative;
}

.a-link--hover-red:hover .a-link__text {
    color: #ea4335;
}

.a-link--hover-red:hover .a-link__icon svg {
    fill: #ea4335;
}

.a-link--hover-underline {
    position: relative;
}

.a-link--hover-underline:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    display: block;
    width: 0;
    height: 1px;
    -webkit-transition: width 250ms ease-out;
    -o-transition: width 250ms ease-out;
    transition: width 250ms ease-out;
    background-color: currentColor;
}

.a-link--hover-underline:hover:after {
    width: 100%;
}

.a-link--hover-block .a-link--hover-underline {
    position: relative;
}

.a-link--hover-block .a-link--hover-underline:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    display: block;
    width: 0;
    height: 1px;
    -webkit-transition: width 250ms ease-out;
    -o-transition: width 250ms ease-out;
    transition: width 250ms ease-out;
    background-color: currentColor;
}

.a-link--hover-block:hover .a-link--hover-underline:after {
    width: 100%;
}

p {
    display: block;
    margin-top: 0;
    margin-bottom: 2rem;
    color: #000000;
    font-family: "SetupGrotesk-Regular", Arial, sans-serif;
    font-weight: 400;
    line-height: 1.45;
    font-size: 16px;
    font-size: 1rem;
    -webkit-transition: font-size 0.3s cubic-bezier(0.16, 0.08, 0.355, 1);
    -o-transition: font-size 0.3s cubic-bezier(0.16, 0.08, 0.355, 1);
    transition: font-size 0.3s cubic-bezier(0.16, 0.08, 0.355, 1);
}

p a {
    color: currentColor;
}

.p--size-12 p {
    font-size: 12px;
    font-size: 0.75rem;
}

.p--size-14 p {
    font-size: 14px;
    font-size: 0.875rem;
}

.p--size-18 p {
    font-size: 18px;
    font-size: 1.125rem;
}

.p--size-20 p {
    font-size: 20px;
    font-size: 1.25rem;
}

@media (max-width: 575px) {
    .p--xs-size-14 p {
        font-size: 14px;
        font-size: 0.875rem;
    }

    .p--xs-size-12 p {
        font-size: 12px;
        font-size: 0.75rem;
    }
}

.p--line-1 p {
    line-height: 1;
}

.p--line-1-2 p {
    line-height: 1.2;
}

.p--line-1-4 p {
    line-height: 1.4;
}

.p--line-1-5 p {
    line-height: 1.5;
}

.p--line-1-6 p {
    line-height: 1.6;
}

.p--line-1-8 p {
    line-height: 1.8;
}

.p--line-2 p {
    line-height: 2;
}

.p--margin-0 p {
    margin-bottom: 0;
}

.p--margin-01 p {
    margin-bottom: 0.1rem;
}

.p--margin-02 p {
    margin-bottom: 0.2rem;
}

.p--margin-03 p {
    margin-bottom: 0.3rem;
}

.p--margin-04 p {
    margin-bottom: 0.4rem;
}

.p--margin-05 p {
    margin-bottom: 0.5rem;
}

.p--margin-06 p {
    margin-bottom: 0.6rem;
}

.p--margin-07 p {
    margin-bottom: 0.7rem;
}

.p--margin-08 p {
    margin-bottom: 0.8rem;
}

.p--margin-09 p {
    margin-bottom: 0.9rem;
}

.p--margin-1 p {
    margin-bottom: 1rem;
}

.p--margin-1-2 p {
    margin-bottom: 1.2rem;
}

.p--margin-1-5 p {
    margin-bottom: 1.5rem;
}

.p--margin-2 p {
    margin-bottom: 2rem;
}

.p--margin-2-5 p {
    margin-bottom: 2.5rem;
}

@media (min-width: 576px) {
    .p--xs-margin-0 p {
        margin-bottom: 0;
    }
}

.p--font-medium p {
    font-family: "SetupGrotesk-Medium", Arial, sans-serif;
    font-weight: 500;
}

.p--font-medium p a {
    font-family: "SetupGrotesk-Medium", Arial, sans-serif;
    font-weight: 500;
}

ol,
ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

ul.list-style,
ol.list-style {
    margin-bottom: 2rem;
}

ul.list-style li,
ol.list-style li {
    position: relative;
    display: block;
    margin-bottom: 8px;
    padding-left: 25px;
    color: #000000;
    font-family: "SetupGrotesk-Regular", Arial, sans-serif;
    font-weight: 400;
    line-height: 1.4;
    font-size: 16px;
    font-size: 1rem;
}

ul.list-style a,
ol.list-style a {
    text-decoration: underline;
}

ul.list-style a:focus, ul.list-style a:hover,
ol.list-style a:focus,
ol.list-style a:hover {
    text-decoration: underline;
}

ul.list-style li:before {
    content: '';
    position: absolute;
    top: 9px;
    left: 3px;
    display: block;
    width: 6px;
    height: 6px;
    color: #000000;
    border-radius: 100%;
    background-color: #000000;
}

ol.list-style {
    list-style-position: inside;
    counter-reset: item;
}

ol.list-style li:before {
    content: counter(item) ".";
    counter-increment: item;
    position: absolute;
    top: 0;
    left: 0;
    display: inline-block;
    color: #000000;
    line-height: 1.4;
    font-size: 16px;
    font-size: 1rem;
}

.swiper-arrow {
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    outline: none;
    z-index: 4;
}

.swiper-arrow.swiper-button-disabled {
    opacity: 0.3;
    pointer-events: none;
}

.swiper-arrow--big {
    top: calc(50% - 23px);
}

.swiper-arrow--big.swiper-arrow-next {
    right: 8px;
}

@media (min-width: 771px) {
    .swiper-arrow--big.swiper-arrow-next {
        right: 20px;
    }
}

@media (min-width: 1200px) {
    .swiper-arrow--big.swiper-arrow-next {
        right: -80px;
    }
}

@media (min-width: 1440px) {
    .swiper-arrow--big.swiper-arrow-next {
        right: -150px;
    }
}

.swiper-arrow--big.swiper-arrow-prev {
    left: 8px;
}

@media (min-width: 771px) {
    .swiper-arrow--big.swiper-arrow-prev {
        left: 20px;
    }
}

@media (min-width: 1200px) {
    .swiper-arrow--big.swiper-arrow-prev {
        left: -80px;
    }
}

@media (min-width: 1440px) {
    .swiper-arrow--big.swiper-arrow-prev {
        left: -150px;
    }
}

@media (min-width: 771px) {
    .swiper-arrow--big .swiper-arrow-content {
        width: 65px;
        height: 65px;
    }

    .swiper-arrow--big .swiper-arrow-content svg {
        font-size: 32px;
        font-size: 2rem;
    }
}

.swiper-arrow--top-right {
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0);
    top: 22px;
}

@media (min-width: 576px) {
    .swiper-arrow--top-right {
        top: 35px;
    }
}

.swiper-arrow--top-right.swiper-arrow-next {
    right: 22px;
}

@media (min-width: 576px) {
    .swiper-arrow--top-right.swiper-arrow-next {
        right: 35px;
    }
}

.swiper-arrow--top-right.swiper-arrow-prev {
    left: auto;
    right: 70px;
}

@media (min-width: 576px) {
    .swiper-arrow--top-right.swiper-arrow-prev {
        right: 85px;
    }
}

.swiper-arrow-next {
    right: 0;
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1;
}

.swiper-arrow-prev {
    left: 0;
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0;
}

.swiper-arrow-content {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    height: 35px;
    width: 35px;
    cursor: pointer;
    background-color: #000000;
    border: 1px solid #000000;
}

.swiper-arrow-content:focus, .swiper-arrow-content:hover {
    background-color: #ffffff;
}

.swiper-arrow-content:focus svg, .swiper-arrow-content:hover svg {
    fill: #000000;
}

.swiper-arrow-content svg {
    fill: #ffffff;
    font-size: 12px;
    font-size: 0.75rem;
}

.swiper-pagination {
    position: relative;
    width: 100%;
}

.swiper-pagination-bullet {
    position: relative;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 0;
    opacity: 1;
    background: transparent;
}

.swiper-pagination-bullet:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    display: block;
    width: 12px;
    height: 12px;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    background-color: #000000;
    border-radius: 100%;
}

.swiper-pagination-bullet-active:after {
    background-color: #000000;
}

.swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet {
    margin: 0;
}

.select2-container {
    z-index: 9;
}

.select2-container.select2-container--open .select2-selection--single .select2-selection__arrow:after,
.select2-container.select2-container--open .select2-selection--multiple .select2-selection__arrow:after,
.select2-container .select2-container--focus .select2-selection--single .select2-selection__arrow:after,
.select2-container .select2-container--focus .select2-selection--multiple .select2-selection__arrow:after {
    background-image: url("/wp-content/themes/scd/assets/icons/chevron-down.svg");
    -webkit-transform: translate(-50%, -50%) rotate(180deg);
    -ms-transform: translate(-50%, -50%) rotate(180deg);
    transform: translate(-50%, -50%) rotate(180deg);
}

.select2-container .select2-search--inline .select2-search__field {
    margin-top: 0;
}

.select2-container .select2-search--inline {
    display: block;
    width: 100%;
}

.select2-container .select2-selection--single,
.select2-container .select2-selection--multiple,
#search_input {
    width: 100%;
    width: 100%;
    height: 55px;
    padding: 0 55px 1px 10px;
    padding-bottom: 1px;
    font-family: "SetupGrotesk-Regular", Arial, sans-serif;
    font-weight: 400;
    line-height: 1.2;
    letter-spacing: 0px;
    color: #000000;
    border: 1px solid #000000;
    outline: none !important;
    background-color: #ffffff;
    font-size: 16px;
    font-size: 1rem;
    border-radius: 0;
}

.select2-container .select2-selection--single .select2-selection__rendered,
.select2-container .select2-selection--multiple .select2-selection__rendered {
    padding: 0;
    line-height: 55px;
    color: #000000;
}

.select2-container .select2-selection--single .select2-selection__placeholder,
.select2-container .select2-selection--multiple .select2-selection__placeholder {
    color: #000000;
}

.select2-container .select2-selection--single .select2-selection__arrow,
.select2-container .select2-selection--multiple .select2-selection__arrow {
    position: absolute;
    top: 0;
    right: 0px;
    width: 55px;
    height: 55px;
    line-height: 55px;
    text-align: center;
}

.select2-container .select2-selection--single .select2-selection__arrow:after,
.select2-container .select2-selection--multiple .select2-selection__arrow:after {
    content: '' !important;
    width: 14px;
    height: 10px;
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    background-image: url("/wp-content/themes/scd/assets/icons/chevron-down.svg");
}

.sorting .select2-container .select2-selection--single .select2-selection__arrow,
.sorting .select2-container .select2-selection--multiple .select2-selection__arrow {
    width: 30px;
    height: 30px;
}

.select2-container .select2-selection--single .select2-selection__arrow b,
.select2-container .select2-selection--multiple .select2-selection__arrow b {
    display: none; 
}


.select2-dropdown {
    border: 1px solid #000000;
    border-radius: 0;
    background-color: white;
}

.select2-dropdown .select2-results__options--nested .select2-results__option {
    padding: 0 25px;
}

.select2-dropdown .select2-results__options {
    max-height: 240px !important;
}

.select2-dropdown .select2-results__options .select2-results__option[aria-selected='true'] {
    color: #000000;
    background-color: #f5f5f5;
}

.select2-dropdown .select2-results__options .select2-results__option {
    padding: 12px 12px;
    font-family: "SetupGrotesk-Regular", Arial, sans-serif;
    font-weight: 400;
    letter-spacing: 0px;
    color: #000000;
    font-size: 16px;
    font-size: 1rem;
}

.select2-dropdown .select2-results__options .text-option .status {
    float: left;
    margin-right: 15px;
}

.select2-dropdown .select2-results__options .text-option .text {
    display: block;
    overflow: hidden;
    height: 100%;
}

.select2-dropdown .select2-results__options .select2-results__option--highlighted {
    color: #000000;
    background-color: #f5f5f5;
}

.select-medium-dropdown.select2-dropdown .select2-results__options .select2-results__option {
    font-family: "SetupGrotesk-Regular", Arial, sans-serif;
    font-weight: 400;
    padding: 12px 15px;
    font-size: 16px;
    font-size: 1rem;
}

/* Content */
section {
    position: relative;
    padding: 1.875rem 0 1.875rem;
    background-color: #ffffff;
}

@media (min-width: 576px) {
    section {
        padding: 3rem 0 3rem;
    }
}

.padding-style-1 {
    padding: 1rem 0 1rem;
}

@media (min-width: 771px) {
    .padding-style-1 {
        padding: 1rem 0 1rem;
    }
}

.padding-style-2 {
    padding: 2rem 0 2rem;
}

@media (min-width: 771px) {
    .padding-style-2 {
        padding: 2rem 0 2rem;
    }
}

.padding-style-3 {
    padding: 2rem 0 2rem;
}

@media (min-width: 771px) {
    .padding-style-3 {
        padding: 3rem 0 3rem;
    }
}

.padding-style-4 {
    padding: 3rem 0 3rem;
}

@media (min-width: 771px) {
    .padding-style-4 {
        padding: 4rem 0 4rem;
    }
}

.padding-style-5 {
    padding: 3rem 0 3rem;
}

@media (min-width: 771px) {
    .padding-style-5 {
        padding: 5rem 0 5rem;
    }
}

.padding-style-6 {
    padding: 3rem 0 3rem;
}

@media (min-width: 771px) {
    .padding-style-6 {
        padding: 6rem 0 6rem;
    }
}

.padding-style-6-3 {
    padding: 4rem 0 3rem;
}

@media (min-width: 576px) {
    .padding-style-6-3 {
        padding: 6rem 0 3rem;
    }
}

.padding-content-1 {
    padding: 1rem 1rem;
}

.padding-content-2 {
    padding: 2rem 2rem;
}

.padding-content-3 {
    padding: 3rem 3rem;
}

.padding-content-4 {
    padding: 4rem 4rem;
}

.padding-content-5 {
    padding: 5rem 5rem;
}

@media (min-width: 771px) {
    .padding-content-2 {
        padding: 2rem 2rem;
    }
}

@media (max-width: 770px) {
    .padding-bp-xs-content-2 {
        padding: 0 2rem 2rem;
    }
}

.content-width-200 {
    max-width: 200px;
}

.content-width-300 {
    max-width: 300px;
}

.content-width-400 {
    max-width: 400px;
}

.content-width-450 {
    max-width: 450px;
}

.content-width-500 {
    max-width: 500px;
}

.content-width-600 {
    max-width: 600px;
}

main {
    position: relative;
    display: block;
    -webkit-box-flex: 1;
    -ms-flex: 1 0 auto;
    flex: 1 0 auto;
    background-color: #ffffff;
}

@media (min-width: 992px) {
    main {
        padding-top: 140px;
    }

    body.kiosk main {
        padding-top: 0;
    }
}

@media (min-width: 992px) {
    .main--secondary-menu {
        padding-top: 160px;
    }

    body.kiosk .main--secondary-menu {
        padding-top: 90px;
    }
}

.container-full {
    margin: 0 auto;
}

.container-small,
.container-normal,
.container-large {
    margin: 0 auto;
    padding: 0 15px;
}

.container-small {
    width: 100%;
}

@media (min-width: 771px) {
    .container-small {
        width: 730px;
    }
}

@media (min-width: 992px) {
    .container-small {
        width: 880px;
    }
}

@media (min-width: 1200px) {
    .container-small {
        width: 880px;
    }
}

@media (min-width: 1440px) {
    .container-small {
        width: 880px;
    }
}

.container-normal {
    width: 100%;
}

@media (min-width: 771px) {
    .container-normal {
        width: 730px;
    }
}

@media (min-width: 992px) {
    .container-normal {
        width: 950px;
    }
}

@media (min-width: 1200px) {
    .container-normal {
        width: 1000px;
    }
}

@media (min-width: 1440px) {
    .container-normal {
        width: 1000px;
    }
}

.container-large {
    width: 100%;
}

@media (min-width: 771px) {
    .container-large {
        width: 730px;
    }
}

@media (min-width: 992px) {
    .container-large {
        width: 950px;
    }
}

@media (min-width: 1200px) {
    .container-large {
        width: 1140px;
    }
}

@media (min-width: 1430px) {
    .container-large {
        width: 1330px;
    }
}

/* Footer */
.footer {
    position: relative;
    z-index: 2;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    padding-top: 2rem;
    background-color: #ffffff;
}

@media (min-width: 576px) {
    .footer {
        padding-top: 6rem;
    }
}

@media (min-width: 992px) {
    .footer {
        padding-top: 12rem;
    }
}

.footer-bg {
    background-color: #000000;
}

.footer-top {
    padding: 3rem 0 2rem;
    background-color: #000000;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
}

@media (min-width: 576px) {
    .footer-top {
        padding: 6rem 0 2rem;
    }
}

.footer-middle {
    background-color: #000000;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    padding: 2rem 0 2rem;
}

@media (min-width: 576px) {
    .footer-middle {
        padding: 4rem 0 2rem;
    }
}

.footer-bottom {
    padding: 2rem 0 2rem;
}

.footer__title {
    display: block;
    margin-bottom: 0.8rem;
    font-weight: 400;
    color: #ffffff;
    font-size: 18px;
    font-size: 1.125rem;
}

.footer__title--small {
    display: block;
    margin-bottom: 0;
    font-weight: 700;
    text-transform: uppercase;
    font-size: 14px;
    font-size: 0.875rem;
    height: 45px;
}

@media (min-width: 771px) {
    .footer__title--small {
        margin-bottom: 0.9375rem;
    }
}

.footer__copy {
    display: block;
    color: rgba(255, 255, 255, 0.6);
    font-size: 16px;
    font-size: 1rem;
    line-height: 1.6;
}

.footer-list__item {
    display: block;
}

.footer-list__link {
    display: inline-block;
    line-height: 1.4;
    color: rgba(255, 255, 255, 0.6);
    padding: 0.15rem 0;
    font-size: 16px;
    font-size: 1rem;
}

.footer-list__link:hover {
    text-decoration: underline;
    color: white;
}

.footer__logo {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    height: 50px;
}

/* Header */
.header__navigation .header__hamburger {
    display: none;
}

@media (max-width: 991px) {
    .header__navigation .container-normal,
    .header__navigation .container-large {
        width: 100%;
    }

    .header__navigation .main-nav {
        position: fixed;
        z-index: 98;
        bottom: 70px;
        left: 0;
        overflow: auto;
        width: 100%;
        height: calc(100% - 70px);
        padding: 5px 20px 0; /* mobile menu fixes */
        -webkit-transform: translateX(calc(100%));
        -ms-transform: translateX(calc(100%));
        transform: translateX(calc(100%));
        background-color: #101010;
        -webkit-transition: none;
        -o-transition: none;
        transition: none;
    }
}

@media (max-width: 991px) and (max-width: 991px) and (min-height: 550px) {
    .header__navigation .main-nav {
        padding: 0 20px 0; /* mobile menu fixes */
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-box-pack: end;
        -ms-flex-pack: end;
        justify-content: flex-end;
    }
}

@media (max-width: 991px) {
    .header__navigation .header__hamburger {
        position: fixed;
        z-index: 200;
        bottom: 0;
        right: 0;
        display: block;
        width: 60px;
        height: 70px;
    }

    .header__navigation .header__hamburger-square {
        display: none;
        position: absolute;
        top: 50%;
        left: 50%;
        pointer-events: none;
        width: 22px;
        height: 22px;
        -webkit-transform: translate(-50%, -50%);
        -ms-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
        border: 2px solid #ffffff;
        z-index: 3;
    }

    .header__navigation .header__hamburger-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        display: inline-block;
        width: 25px;
        height: 3px;
        -webkit-transition-duration: 250ms;
        -o-transition-duration: 250ms;
        transition-duration: 250ms;
        -webkit-transition-property: background-color, -webkit-transform;
        transition-property: background-color, -webkit-transform;
        -o-transition-property: background-color, transform;
        transition-property: background-color, transform;
        transition-property: background-color, transform, -webkit-transform;
        -webkit-transform: translate(-50%, -50%);
        -ms-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
        background-color: #ffffff;
        font-weight: 300;
    }

    .header__navigation .header__hamburger-icon:before, .header__navigation .header__hamburger-icon:after {
        position: absolute;
        display: block;
        width: 25px;
        height: 3px;
        content: '';
        -webkit-transition-duration: 250ms;
        -o-transition-duration: 250ms;
        transition-duration: 250ms;
        -webkit-transition-property: margin, -webkit-transform;
        transition-property: margin, -webkit-transform;
        -o-transition-property: margin, transform;
        transition-property: margin, transform;
        transition-property: margin, transform, -webkit-transform;
        background: #ffffff;
    }

    .header__navigation .header__hamburger-icon:before {
        margin-top: -12px;
    }

    .header__navigation .header__hamburger-icon:after {
        margin-top: 12px;
    }

    .header__navigation.is-collapse .header__hamburger-square {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
    }

    .header__navigation.is-collapse {
        top: calc(100% - 70px);
        -webkit-transition: top 250ms ease;
        -o-transition: top 250ms ease;
        transition: top 250ms ease;
    }

    .header__navigation.is-collapse .header__hamburger {
        top: calc(100% - 70px);
        -webkit-transition: top 250ms ease;
        -o-transition: top 250ms ease;
        transition: top 250ms ease;
    }

    .header__navigation.is-collapse .main-nav {
        bottom: 70px;
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
        -webkit-transition: -webkit-transform 250ms ease;
        transition: -webkit-transform 250ms ease;
        -o-transition: transform 250ms ease;
        transition: transform 250ms ease;
        transition: transform 250ms ease, -webkit-transform 250ms ease;
    }

    .header__navigation.is-collapse .header__hamburger-icon {
        -webkit-transition: none;
        -o-transition: none;
        transition: none;
        background: rgba(0, 0, 0, 0);
    }

    .header__navigation.is-collapse .header__hamburger-icon:before {
        margin-top: 0;
        -webkit-transform: rotate(45deg);
        -ms-transform: rotate(45deg);
        transform: rotate(45deg);
    }

    .header__navigation.is-collapse .header__hamburger-icon:after {
        margin-top: 0;
        -webkit-transform: rotate(-45deg);
        -ms-transform: rotate(-45deg);
        transform: rotate(-45deg);
    }
}

.header__navigation {
    position: fixed;
    z-index: 999;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #000000;
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
}

@media (min-width: 992px) {
    .header__navigation {
        top: 0;
        bottom: auto;
        padding: 0 30px;
        -webkit-box-shadow: none;
        box-shadow: none;
    }
}

@media (min-width: 992px) {
    .header__navigation.is-scrolling.is-scroll-to .header-contact__title,
    .header__navigation.is-scrolling.is-scroll-to .social-list--header,
    .header__navigation.is-scrolling.is-scroll-to .header-offer, .header__navigation.is-scrolling:not(.navigation-scroll-top) .header-contact__title,
    .header__navigation.is-scrolling:not(.navigation-scroll-top) .social-list--header,
    .header__navigation.is-scrolling:not(.navigation-scroll-top) .header-offer {
        display: none !important;
    }

    .header__navigation.is-scrolling.is-scroll-to .main-nav__list--bottom, .header__navigation.is-scrolling:not(.navigation-scroll-top) .main-nav__list--bottom {
        display: none;
    }

    .header__navigation.is-scrolling.is-scroll-to .main-nav__link, .header__navigation.is-scrolling:not(.navigation-scroll-top) .main-nav__link {
        font-family: "SetupGrotesk-Bold", Arial, sans-serif;
        font-weight: 700;
    }

    .header__navigation.is-scrolling.is-scroll-to .header-contact, .header__navigation.is-scrolling:not(.navigation-scroll-top) .header-contact {
        border-left: 0;
        margin-left: 0;
        padding-left: 0;
    }

    .header__navigation.is-scrolling.is-scroll-to .header__logo, .header__navigation.is-scrolling:not(.navigation-scroll-top) .header__logo {
        height: 70px;
        padding: 20px 0 21px;
    }

    .header__navigation.is-scrolling.is-scroll-to .header__logo-image--full, .header__navigation.is-scrolling:not(.navigation-scroll-top) .header__logo-image--full {
        display: none !important;
    }

    .header__navigation.is-scrolling.is-scroll-to .header__logo-image--scroll, .header__navigation.is-scrolling:not(.navigation-scroll-top) .header__logo-image--scroll {
        display: block !important;
    }

    .header__navigation.is-scrolling.is-scroll-to .header__logo-image, .header__navigation.is-scrolling:not(.navigation-scroll-top) .header__logo-image {
        width: 55px;
    }
}

@media (min-width: 992px) {
    .header__flex {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
    }
}

.header__logo {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    position: relative;
    z-index: 99;
    padding: 22px 0 22px;
    text-align: center;
    -webkit-transition: padding 350ms ease;
    -o-transition: padding 350ms ease;
    transition: padding 350ms ease;
    float: left;
    -webkit-transition: height 350ms ease, padding 350ms ease;
    -o-transition: height 350ms ease, padding 350ms ease;
    transition: height 350ms ease, padding 350ms ease;
}

@media (min-width: 992px) {
    .header__logo {
        float: left;
        width: 155px;
        height: 140px;
        padding: 40px 0 41px;
    }
}

.header__logo-link {
    display: inline-block;
    vertical-align: middle;
}

.header__logo-image {
    margin: 0 auto;
    -webkit-transition: width 350ms ease;
    -o-transition: width 350ms ease;
    transition: width 350ms ease;
}

@media (max-width: 991px) {
    .header__logo-image {
        width: 60px;
    }
}

@media (min-width: 992px) {
    .header__logo-image--full {
        display: block !important;
    }
}

@media (min-width: 992px) {
    .header__logo-image--scroll {
        display: none !important;
    }
}

@media (min-width: 992px) {
    .main-nav {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: justify;
        -ms-flex-pack: justify;
        justify-content: space-between;
        padding-left: 20px;
        width: auto;
        -webkit-box-flex: 1;
        -ms-flex: 1;
        flex: 1;
    }
}

@media (min-width: 992px) {
    .main-nav__list {
        display: -webkit-box;
        display: -ms-flexbox;
        display: inline-flex;
        vertical-align: bottom;
    }
}

@media (max-width: 991px) {
    .main-nav__list--columns, .main-nav__list--secondary { /* mobile menu fixes */
        padding-bottom: 15px;
        padding-left: 7px; /* mobile menu fixes */
        padding-right: 7px;
    }

    .main-nav__list--secondary { /* mobile menu fixes */
        display: flex;
        flex-direction: column-reverse;
    }

    .main-nav__list--columns {
        display: grid; /* mobile menu fixes */
        grid-auto-flow: column;
        grid-template-columns: 1fr 1fr;
        grid-template-rows: repeat(4, 1fr);
    }
}

@media (max-width: 991px) {
    /* mobile menu fixes */
    .main-nav__list--secondary .main-nav__item:first-child {
        display: none;
    }
}

@media (max-width: 991px) {
    .main-nav__list--bottom, .has-secondary .main-nav__list--columns { /* mobile menu fixes */
        background-color: #000000;
        margin: 0 -20px; /* mobile menu fixes */
        padding: 20px; /* mobile menu fixes */
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: reverse;
        -ms-flex-direction: column-reverse;
        flex-direction: column-reverse;
        -webkit-box-align: end;
        -ms-flex-align: end;
        align-items: flex-end;
    }
}

@media (max-width: 991px) {
    .has-secondary .main-nav__list--bottom { /* mobile menu fixes */
        display: none;
    }
}

@media (min-width: 992px) {
    .main-nav__list--secondary { /* mobile menu fixes */
        display: none;
    }
}

.main-nav__list--bottom .main-nav__link { /* mobile menu fixes */
    font-family: "SetupGrotesk-Bold", Arial, sans-serif;
    font-weight: 700;
}

@media (max-width: 991px) {
    .has-secondary .main-nav__list--columns .main-nav__link {
        font-weight: 700;
    }

    .main-nav__list--bottom .main-nav__link, .has-secondary .main-nav__list--columns .main-nav__link { /* mobile menu fixes */
        font-size: 24px;
        font-size: 1.5rem;
    }
}

@media (min-width: 992px) {
    .main-nav__item {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        margin: 0 8px;
    }
}

@media (min-width: 1440px) {
    .main-nav__item {
        margin: 0 12px;
    }
}

@media (max-width: 991px) {
    .main-nav__list--bottom .main-nav__item, .has-secondary .main-nav__list--columns .main-nav__item { /* mobile menu fixes */
        display: block;
        text-align: right;
        margin: 3px 0;
    }

    .main-nav__list--columns .main-nav__item:first-child { /* mobile menu fixes */
        grid-row-start: 2;
    }

    .has-secondary .main-nav__list--columns .main-nav__item:last-child { /* mobile menu fixes */
        display: none;
    }
}

.main-nav__item:focus .main-nav__link, .main-nav__item:hover .main-nav__link,
.main-nav__item.is-active .main-nav__link, .main-nav__item.current-menu-item .main-nav__link {
    color: #ffffff;
    text-decoration: underline; /* menu hover efekt fix */
}

/*.main-nav__item:focus .main-nav__link:after, .main-nav__item:hover .main-nav__link:after, .main-nav__item.is-active .main-nav__link:after {
  width: 100%;
}*/

.main-nav__link {
    position: relative;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 0;
    text-decoration: none;
    color: #ffffff;
    min-height: 35px;
    line-height: 1;
    font-family: "SetupGrotesk-Regular", Arial, sans-serif;
    font-weight: 400;
    font-size: 18px;
    font-size: 1.125rem;
}

@media (min-width: 992px) {
    .main-nav__link {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        font-size: 14px;
        font-size: 0.875rem;
    }
}

@media (min-width: 1440px) {
    .main-nav__link {
        font-size: 16px;
        font-size: 1rem;
    }
}

/* menu hover efekt fix */
/*.main-nav__link:after {
  position: absolute;
  bottom: 0;
  left: 0;
  display: block;
  width: 0;
  height: 1px;
  content: '';
  -webkit-transition: width 250ms ease-out;
  -o-transition: width 250ms ease-out;
  transition: width 250ms ease-out;
  background-color: #ffffff;
}*/

@media (min-width: 993px) and (max-width: 1200px) {
    .header-contact {
        margin-top: 10px;
        padding-top: 5px;
        border-top: 1px solid #ffffff;
    }
}

@media (min-width: 1201px) {
    .header-contact {
        margin-left: 30px;
        padding-left: 30px;
        border-left: 1px solid #ffffff;
    }
}

.header__right-panel {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    flex-shrink: 0;
}

@media (max-width: 991px) {
    /* mobile menu fixes */
    .header__right-panel {
        padding-left: 7px;
        padding-right: 7px;
    }
}

@media (min-width: 993px) and (max-width: 1200px) {
    .header__right-panel:not(.d-lg-none) {
        display: block !important;
    }
}

.header-contact__top-block {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
}

.header-contact__title {
    display: inline-block;
    margin-right: 0.9375rem;
    font-size: 16px;
    font-size: 1rem;
    color: #ffffff;
    font-family: "SetupGrotesk-Regular", Arial, sans-serif;
    font-weight: 400;
}

.header-contact__title:last-child {
    margin-right: 0.5rem;
    line-height: 35px;
}

@media (min-width: 993px) and (max-width: 1200px) {
    .header-contact__title {
        font-size: 14px;
        font-size: 0.875rem;
    }
}

.language__link {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: flex-end; /* mobile menu fixes */
    width: 35px;
    height: 35px;
    text-transform: uppercase;
    color: #ffffff;
    font-family: "SetupGrotesk-Regular", Arial, sans-serif;
    font-weight: 400;
    font-size: 18px;
    font-size: 1.125rem;
    margin-bottom: 10px;
}

@media (min-width: 992px) {
    .language__link {
        justify-content: center; /* mobile menu fixes */
        margin-bottom: 0;
        font-size: 16px;
        font-size: 1rem;
    }
}

@media (min-width: 993px) and (max-width: 1200px) {
    .language__link {
        height: 25px;
    }
}

.language__link.is-active {
    font-family: "SetupGrotesk-Bold", Arial, sans-serif;
    font-weight: 700;
}

@media (max-width: 991px) {
    .header__left-panel {
        text-align: right;
    }
}

/* Social list */
.social-list {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-left: -12px;
}

.social-list--header {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    margin-left: 0;
    margin-right: -4px;
}

@media (min-width: 993px) and (max-width: 1200px) {
    .social-list--header .social-list__link {
        height: 25px;
    }
}

.social-list--header .social-list__link svg {
    font-size: 16px;
    font-size: 1rem;
}

.social-list__item {
    display: inline-block;
}

.social-list__link {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    text-align: center;
    width: 40px;
    height: 35px;
    text-align: center;
    vertical-align: middle;
}

.social-list__link svg {
    font-size: 20px;
    font-size: 1.25rem;
    fill: rgba(255, 255, 255, 0.6);
}

.social-list__link:focus svg, .social-list__link:hover svg {
    fill: #ffffff;
}

.box {
    position: relative;
}

.box--shadow {
    -webkit-box-shadow: 0 26px 42px rgba(0, 0, 0, 0.15);
    box-shadow: 0 26px 42px rgba(0, 0, 0, 0.15);
}

.box--border {
    border: 1px solid #000000;
}

.box--border-white {
    border: 1px solid #ffffff;
}

@media (min-width: 992px) {
    .box--md-border-white {
        border: 1px solid #ffffff;
    }
}

.box--size-2-2 {
    padding: 1.5625rem 1.25rem 2.1875rem;
}

@media (min-width: 576px) {
    .box--size-2-2 {
        padding: 1.6rem 2rem 1rem;
    }
}

.box--size-2-5-2 {
    padding: 1.5625rem 1.25rem 0.625rem;
}

@media (min-width: 576px) {
    .box--size-2-5-2 {
        padding: 2.2rem 2rem 2rem;
    }
}

.box--size-2-5-3 {
    padding: 1.5625rem 1.25rem 1.25rem;
}

@media (min-width: 576px) {
    .box--size-2-5-3 {
        padding: 2.2rem 2rem 2rem;
    }
}

.box--size-4-5-2 {
    padding: 1.5625rem 1.25rem 0.625rem;
}

@media (min-width: 576px) {
    .box--size-4-5-2 {
        padding: 4rem 2rem 1rem;
    }
}

.box--size-2-5-5 {
    padding: 1.5625rem 1.25rem 1.875rem;
}

@media (min-width: 576px) {
    .box--size-2-5-5 {
        padding: 2.2rem 2rem 2rem;
    }
}

.box--size-0-5-2 {
    padding: 1.5625rem 1.25rem 0.625rem;
}

@media (min-width: 576px) {
    .box--size-0-5-2 {
        padding: 2.2rem 2rem 2rem;
    }
}

@media (min-width: 992px) {
    .box--size-0-5-2 {
        padding: 0 2rem 2rem;
    }
}

/* Mobile-specific override for the people section - disable border and padding */
@media (max-width: 575px) {
    #main-content .box.box--border.box--size-0-5-2 {
        border: none !important;
        padding: 0 !important;
    }
}

@media (min-width: 576px) {
    .box--equal-height-btn {
        height: calc(100% - 55px);
    }

    .box--equal-height {
        height: calc(100%);
    }
}

@media (min-width: 992px) {
    .box--md-equal-height-btn {
        height: calc(100% - 55px);
    }
}

@media (min-width: 992px) {
    .box--md-equal-height {
        height: 100%;
    }
}

@media (min-width: 992px) {
    .box--md-two-column {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
    }

    .box--md-two-column .box-top {
        -webkit-box-flex: 1;
        -ms-flex: 1 0 auto;
        flex: 1 0 auto;
    }

    .box--md-two-column .box--size-2-2 {
        -ms-flex-negative: 0;
        flex-shrink: 0;
    }
}

@media (min-width: 992px) {
    .box--md-offset-left {
        position: relative;
        z-index: 2;
        left: -1px;
    }
}

/* Card block */
.card__item {
    margin-bottom: 1.8rem;
}

@media (min-width: 576px) {
    .card__item {
        margin-bottom: 1.8rem;
    }
}

.card__item--big {
    margin-bottom: 2rem;
}

@media (min-width: 576px) {
    .card__item--big {
        margin-bottom: 2.5rem;
    }
}


.card__item--row {
    margin-bottom: 1.875rem;
}

@media (min-width: 576px) {
    .card__item--row {
        margin-bottom: 2.5rem;
    }

    .card__item--row .card__content {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
    }

    .card__item--row .card__text-block {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-box-pack: justify;
        -ms-flex-pack: justify;
        justify-content: space-between;
        padding-left: 2.5rem;
        width: auto;
        max-width: auto;
        -webkit-box-flex: 1;
        -ms-flex: 1;
        flex: 1;
    }
}

@media (min-width: 576px) {
    .card__item--row-center .card__content {
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
    }

    .card__item--row-center .card__text-block {
        padding-left: 2.5rem;
        padding-top: 0;
    }
}

@media (min-width: 576px) and (min-width: 993px) and (max-width: 1200px) {
    .card__item--row-center .card__text-block {
        padding-left: 1.25rem;
    }
}

@media (min-width: 993px) and (max-width: 1200px) {
    .card__item--row-center .card__title {
        font-size: 20px;
        font-size: 1.25rem;
    }
}


@media (min-width: 771px) {
    .card__item--date-absolute-ext .card__title,
    .card__item--date-absolute .card__title {
        padding-right: 6.25rem;
    }
}

@media (min-width: 993px) and (max-width: 1200px) {
    .card__item--date-absolute-ext .card__title,
    .card__item--date-absolute .card__title {
        padding-right: 0;
    }
}

@media (min-width: 772px) and (max-width: 992px) {
    .card__item--date-absolute-ext .card__title,
    .card__item--date-absolute .card__title {
        font-size: 20px;
        font-size: 1.25rem;
    }
}

@media (min-width: 577px) and (max-width: 771px) {
    .card__item--date-absolute-ext .card__image-block--w-xs-270,
    .card__item--date-absolute .card__image-block--w-xs-270 {
        width: 180px;
        max-width: 180px;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 180px;
        flex: 0 0 180px;
    }
}

@media (min-width: 993px) and (max-width: 1200px) {
    .card__item--date-absolute-ext .card__image-block--w-xs-270,
    .card__item--date-absolute .card__image-block--w-xs-270 {
        width: 180px;
        max-width: 180px;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 180px;
        flex: 0 0 180px;
    }
}

.card__item--date-absolute-ext .card-info,
.card__item--date-absolute .card-info {
    margin-bottom: 0.5rem;
}

@media (min-width: 771px) {
    .card__item--date-absolute-ext .card-info {
        margin-bottom: 2rem;
    }
}

@media (min-width: 993px) and (max-width: 1200px) {
    .card__item--date-absolute-ext .card-info,
    .card__item--date-absolute .card-info {
        margin-bottom: 1.2rem;
    }
}

@media (min-width: 771px) {
    .card__item--date-absolute-ext .card__title {
        padding-right: 12.5rem;
    }
}

@media (min-width: 993px) and (max-width: 1200px) {
    .card__item--date-absolute-ext .card__title {
        padding-right: 0;
    }
}

@media (min-width: 992px) {
    .card__item--date-absolute-ext .card-info__item {
        font-size: 18px;
        font-size: 1.125rem;
    }
}

@media (min-width: 1200px) {
    .card__item--date-absolute-ext .tag-list {
        padding-right: 12.5rem;
    }
}

@media (min-width: 993px) and (max-width: 1200px) {
    .card__item--date-absolute-ext .tag-list {
        padding-right: 0;
    }
}

.card__item--row-big-spacing {
    margin-bottom: 1.875rem;
}

@media (min-width: 576px) {
    .card__item--row-big-spacing {
        margin-bottom: 3.4375rem;
    }
}

.card__item--five {
    width: 33.33%;
    max-width: 33.33%;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.33%;
    flex: 0 0 33.33%;
    margin-bottom: 0.625rem;
}

@media (min-width: 771px) {
    .card__item--five {
        margin-bottom: 1.25rem;
        width: 20%;
        max-width: 20%;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 20%;
        flex: 0 0 20%;
    }
}

.card__item--three {
    width: 33.33%;
    max-width: 33.33%;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.33%;
    flex: 0 0 33.33%;
    margin-bottom: 0.625rem;
}

@media (min-width: 771px) {
    .card__item--three {
        margin-bottom: 1.25rem;
    }
}

.card__content {
    display: block;
    overflow: hidden;
    position: relative;
}

.card__content:focus img, .card__content:hover img {
    -webkit-transform: scale(1.05, 1.05);
    -ms-transform: scale(1.05, 1.05);
    transform: scale(1.05, 1.05);
}

.card__content:focus .card__title a, .card__content:hover .card__title a {
    text-decoration: underline;
}

.card__title {
    font-family: "SetupGrotesk-Medium", Arial, sans-serif;
    font-weight: 500;
}

.card__title a {
    font-family: "SetupGrotesk-Medium", Arial, sans-serif;
    font-weight: 500;
}

.card__image-block {
    display: block;
    overflow: hidden;
    position: relative;
    z-index: 0;
}

.card__image-block:empty {
    padding: 0;
    height: 0;
}

.card__image-block img {
    -webkit-transition: -webkit-transform 400ms ease-out;
    transition: -webkit-transform 400ms ease-out;
    -o-transition: transform 400ms ease-out;
    transition: transform 400ms ease-out;
    transition: transform 400ms ease-out, -webkit-transform 400ms ease-out;
    will-change: transform;
}

@media (min-width: 576px) {
    .card__image-block--h-xs-120 {
        height: 120px;
    }
}

@media (min-width: 576px) {
    .card__image-block--h-xs-190 {
        height: 190px;
    }
}

@media (min-width: 576px) {
    .card__image-block--h-xs-220 {
        height: 220px;
    }
}

@media (min-width: 576px) {
    .card__image-block--h-xs-230 {
        height: 230px;
    }
}

@media (min-width: 576px) {
    .card__image-block--h-xs-260 {
        height: 260px;
    }
}

@media (min-width: 576px) {
    .card__image-block--h-xs-280 {
        min-height: 280px;
    }
}

@media (min-width: 576px) {
    .card__image-block--h-xs-320 {
        min-height: 320px;
    }
}

@media (min-width: 576px) {
    .card__image-block--h-xs-350 {
        min-height: 350px;
    }
}

@media (min-width: 576px) {
    .card__image-block--h-xs-380 {
        min-height: 380px;
    }
}

@media (min-width: 576px) {
    .card__image-block--h-xs-400 {
        min-height: 400px;
    }
}

@media (min-width: 576px) {
    .card__image-block--h-xs-420 {
        min-height: 420px;
    }
}

@media (min-width: 576px) {
    .card__image-block--w-xs-370 {
        width: 370px;
    }
}

@media (min-width: 577px) and (max-width: 771px) {
    .card__image-block--w-xs-370 {
        width: 220px;
        max-width: 220px;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 220px;
        flex: 0 0 220px;
    }
}

@media (min-width: 772px) and (max-width: 992px) {
    .card__image-block--w-xs-370 {
        width: 220px;
        max-width: 220px;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 220px;
        flex: 0 0 220px;
    }
}

@media (min-width: 576px) {
    .card__image-block--w-xs-400 {
        width: 220px;
    }
}

@media (min-width: 992px) {
    .card__image-block--w-xs-400 {
        width: 400px;
    }
}

@media (min-width: 576px) {
    .card__image-block--w-xs-420 {
        width: 420px;
    }
}

@media (min-width: 576px) {
    .card__image-block--w-xs-460 {
        width: 260px;
    }
}

@media (min-width: 992px) {
    .card__image-block--w-xs-460 {
        width: 460px;
    }
}

@media (min-width: 993px) and (max-width: 1200px) {
    .card__image-block--w-xs-460 {
        width: 260px;
        max-width: 260px;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 260px;
        flex: 0 0 260px;
    }
}

@media (min-width: 576px) {
    .card__image-block--w-xs-270 {
        width: 270px;
        max-width: 270px;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 270px;
        flex: 0 0 270px;
    }
}

@media (min-width: 993px) and (max-width: 1200px) {
    .card__image-block--w-xs-270 {
        width: 220px;
        max-width: 220px;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 220px;
        flex: 0 0 220px;
    }
}

@media (min-width: 576px) {
    .card__image-block--w-xs-270-side {
        width: 270px;
        max-width: 270px;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 270px;
        flex: 0 0 270px;
    }
}

@media (min-width: 993px) and (max-width: 1200px) {
    .card__image-block--w-xs-270-side {
        width: 180px;
        max-width: 180px;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 180px;
        flex: 0 0 180px;
    }
}

@media (min-width: 576px) {
    .card__image-block--w-xs-100 {
        width: 100px;
        max-width: 100px;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100px;
        flex: 0 0 100px;
    }
}

.card__text-block {
    position: relative;
}

.card__text-block--top {
    padding-top: 1.5rem;
}

@media (min-width: 576px) {
    .card__text-block--top {
        padding-top: 1.5rem;
    }
}

.card__tag-list {
    position: absolute;
    top: 10px;
    left: 10px;
}

.card__tag-list-item {
    display: inline-block;
    border-radius: 10px;
    padding: 5px 10px;
    color: #ffffff;
    font-weight: 700;
    margin-bottom: 5px;
    background-color: #000000;
    font-size: 14px;
    font-size: 0.875rem;
}

.card-info {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}

.card-info--big .card-info__item:not(:last-child) {
    margin-right: 1rem;
    padding-right: 1rem;
    border-right: 1px solid #000000;
}

.card-info--big .card-info__link {
    font-size: 16px;
    font-size: 1rem;
    text-decoration: none;
}

.card-info--big .card-info__link:hover {
    text-decoration: underline;
}

@media (min-width: 771px) {
    .card-info--absolute {
        position: absolute;
        top: 2rem;
        right: 0;
    }
}

@media (min-width: 993px) and (max-width: 1200px) {
    .card-info--absolute {
        position: relative;
        top: auto;
    }
}

.card-info__item {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    margin-bottom: 0.5rem;
    line-height: 1;
    font-weight: 400;
    font-size: 14px;
    font-size: 0.875rem;
    color: #202020;
}

@media (min-width: 576px) {
    .card-info__item {
        font-size: 16px;
        font-size: 1rem;
    }
}

.card-info__item:not(:last-child) {
    margin-right: 0.4rem;
    padding-right: 0.4rem;
    border-right: 1px solid rgba(111, 111, 111, 0.5);
}

.card-info__link {
    text-decoration: underline;
    /*   line-height: 1.2; */
    font-weight: 400;
    font-size: 14px;
    font-size: 0.875rem;
    color: #202020;
}

span.card-info__link {
    text-decoration: none;
}

@media (min-width: 576px) {
    .card-info__link {
        font-size: 16px;
        font-size: 1rem;
    }
}

.card-info__link--gray {
    color: rgba(0, 0, 0, 0.5);
}

.card__date {
    line-height: 1;
    font-weight: 400;
    font-size: 14px;
    font-size: 0.875rem;
}

/* tag list */
.tag-list {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 0.9375rem;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}

.tag-list--column {
    display: block;
    margin-bottom: 0;
}

.tag-list--column .tag-list__item {
    display: block;
}

.tag-list--no-margin {
    margin-bottom: 0;
}

.tag-list--no-margin .tag-list__item {
    margin-top: 0.625rem;
    margin-bottom: 0;
}

.tag-list--no-margin-ext {
    margin-bottom: 0;
}

.tag-list--no-margin-ext .tag-list__item {
    margin-bottom: 0;
}

.tag-list--top {
    position: relative;
    margin-top: -1px;
}

.tag-list__item {
    display: inline-block;
    margin-right: 0.625rem;
    margin-bottom: 0.625rem;
}

.tag-list__link {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    min-height: 28px;
    text-align: left;
    vertical-align: middle;
    padding: 0.15rem 0.375rem;
    text-transform: uppercase;
    color: #202020;
    background-color: transparent;
    font-size: 14px;
    font-size: 0.875rem;
    border: 1px solid #202020;
    font-weight: 400;
    font-family: "SetupGrotesk-Regular", Arial, sans-serif;
}

.tag-list__link.is-selected .tag-list__close {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
}

.tag-list__link.is-active, .tag-list__link:focus, .tag-list__link:hover, .tag-list__link.is-selected { /* filter clankov fixes */
    border: 1px solid #202020;
    color: #ffffff;
    background-color: #202020;
}

.tag-list__link.is-active .tag-list__close svg, .tag-list__link:focus .tag-list__close svg, .tag-list__link:hover .tag-list__close svg, .tag-list__link.is-selected .tag-list__close svg { /* filter clankov fixes */
    fill: #ffffff;
}

.tag-list__close {
    display: none;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    height: 20px;
    width: 25px;
    margin-left: 0.3125rem;
    margin-right: -5px;
}

.tag-list__close svg {
    fill: #202020;
    font-size: 12px;
    font-size: 0.75rem;
}

/* Load more content block */
.load-more.is-last .load-more {
    height: auto;
}

.load-more.is-last .load-more__button {
    display: none;
}

.load-more.is-disabled .load-more__button {
    display: none;
}

.load-more__spinner {
    z-index: 1;
    display: none;
}

.load-more__spinner.is-active {
    display: inline-block;
}

.load-more__button {
    cursor: pointer;
}

/* Breadcrumbs block */
.breadcrumbs {
    margin-bottom: 1.2rem;
}

.breadcrumb__item {
    display: inline-block;
    vertical-align: middle;
}

.breadcrumb__item:last-child .breadcrumb__link:after {
    content: none;
}

.breadcrumb__link {
    display: inline-block;
    vertical-align: middle;
    line-height: 1.2;
    color: #828282;
    font-size: 14px;
    font-size: 0.875rem;
    margin-right: 1rem;
    margin-bottom: 0.8rem;
    position: relative;
    text-decoration: underline;
}

.breadcrumb__link:focus, .breadcrumb__link:hover {
    color: #000000;
}

.breadcrumb__link:after {
    content: '';
    position: absolute;
    right: -15px;
    top: 2px;
    width: 7px;
    height: 11px;
    display: inline-block;
    vertical-align: middle;
    line-height: 1;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    background-image: url("/wp-content/themes/scd/assets/icons/chevron-right-gray.svg");
}

/* Modal block */
.modal__overlay {
    position: fixed;
    z-index: 9999;
    top: 0;
    left: 0;
    height: 0;
    opacity: 0;
    visibility: hidden;
    overflow: hidden;
    background-color: rgba(35, 35, 35, 0.95);
    -webkit-transition: opacity 400ms ease;
    -o-transition: opacity 400ms ease;
    transition: opacity 400ms ease;
}

.modal__overlay.is-active {
    width: 100%;
    height: 100%;
    opacity: 1;
    overflow: visible;
    visibility: visible;
}

.modal {
    position: fixed;
    z-index: 999999;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    visibility: hidden;
    overflow: hidden;
    width: auto;
    height: 0;
    padding: 3rem 0 2rem;
    opacity: 0;
    -webkit-overflow-scrolling: touch;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-transition: opacity 400ms ease;
    -o-transition: opacity 400ms ease;
    transition: opacity 400ms ease;
}

@media (min-width: 771px) {
    .modal {
        padding: 6rem 0 4rem;
    }
}

.modal.is-active {
    visibility: visible;
    overflow-x: hidden;
    overflow-y: auto;
    width: 100%;
    height: 100%;
    opacity: 1;
    -webkit-transition: opacity 400ms ease;
    -o-transition: opacity 400ms ease;
    transition: opacity 400ms ease;
}

.modal.is-active .modal__box {
    position: relative;
    top: auto;
    left: auto;
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0);
}

.modal.modal__hidden .modal__close,
.modal.modal__hidden .modal__scroll {
    display: none;
}

.modal__scroll {
    margin: auto 1rem;
    height: 100%; /* fix horizontal swiper scroll */
}

.modal__box {
    margin: 0 auto;
    padding: 0;
    width: 320px;
    height: 100%; /* fix horizontal swiper scroll */
}

@media (min-width: 370px) {
    .modal__box {
        width: 370px;
    }
}

@media (min-width: 576px) {
    .modal__box {
        width: 500px;
    }
}

@media (min-width: 771px) {
    .modal__box {
        width: 700px;
    }
}

@media (min-width: 992px) {
    .modal__box {
        width: 900px;
    }
}

@media (min-width: 1200px) {
    .modal__box {
        width: 1000px;
    }
}

.modal__close {
    position: fixed;
    z-index: 99;
    top: 0;
    right: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    width: 50px;
    height: 50px;
    line-height: 50px;
    cursor: pointer;
    text-align: center;
    background: #000000;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}

.modal__close svg {
    fill: #ffffff;
    font-size: 22px;
    font-size: 1.375rem;
}

.modal__close:focus, .modal__close:hover {
    background: #ffffff;
}

.modal__close:focus svg, .modal__close:hover svg {
    fill: #000000;
}

.modal__body {
    width: 100%;
    height: 100%; /* fix vertical swiper scroll */
}

.modal__body .js-slider-group.w-100, .swiper-container {
    height: 100%; /* fix vertical swiper scroll */
}

.modal__body p {
    margin-top: 2rem;
    text-align: center;
}

.modal__body .swiper-slide {
    position: relative;
    display: flex;
    flex-flow: column;
    place-content: center;
}

.modal__body .swiper-slide img {
    min-height: 0;
}

/* Dropdown block */
.dropdown {
    position: relative;
}

@media (min-width: 770px) {
    .dropdown:focus .dropdown__content, .dropdown:hover .dropdown__content {
        visibility: visible;
        height: auto;
        opacity: 1;
    }
}

.dropdown.is-active .dropdown__content {
    visibility: visible;
    height: auto;
    opacity: 1;
}

.dropdown .dropdown__trigger {
    cursor: pointer;
}

.dropdown .dropdown__trigger:after {
    content: '';
    font-size: 16px;
    font-size: 1rem;
    width: 28px;
    height: 19px;
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    background-repeat: no-repeat;
    opacity: 0.5;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    background-position: center;
    background-size: cover;
    background-image: url("/wp-content/themes/scd/assets/icons/chevron-down.svg");
}

.dropdown .dropdown__content {
    position: absolute;
    top: 100%;
    right: 0;
    visibility: hidden;
    overflow: hidden;
    width: 100%;
    height: 0;
    opacity: 0;
    background-color: white;
    -webkit-box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.4);
    box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.4);
}

@media (max-width: 770px) {
    .dropdown .dropdown__content {
        position: relative;
        top: auto;
        right: auto;
        background-color: transparent;
    }
}

@media (min-width: 771px) {
    .dropdown--sm {
        display: none;
    }
}

.dropdown-list {
    text-align: center;
    margin-top: 1rem;
    margin-bottom: 1rem;
}

@media (min-width: 992px) {
    .dropdown-list {
        text-align: left;
        margin: 0;
    }
}

.dropdown-list__item {
    display: block;
}

@media (min-width: 992px) {
    .dropdown-list__item {
        display: block;
    }
}

.dropdown-list__item:last-child .dropdown-list__link {
    border-bottom: 0;
}

.dropdown-list__item.is-active .dropdown__link {
    font-family: "SetupGrotesk-Regular", Arial, sans-serif;
    font-weight: 700;
    opacity: 1;
}

.dropdown-list__link {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 1rem 1rem;
    min-height: 50px;
    line-height: 1.2;
    font-size: 16px;
    font-size: 1rem;
    text-decoration: none;
    color: #ffffff;
    border-bottom: 1px solid #ffffff;
    text-align: left;
}

.hero-section {
    padding: 0;
    overflow: hidden;
}

@media (max-width: 770px) {
    .hero-section {
        margin-bottom: 0;
    }

    .hero-section .container-large {
        padding: 0;
    }
}

@media (min-width: 771px) {
    .hero-section {
        padding: 3rem 0 4rem;
    }
}

.hero-section.bg-black + section {
    padding-top: 4rem;
}

@media (min-width: 576px) {
    .hero-section.bg-black + section {
        padding-top: 6rem;
    }
}

.hero-section-top {
    padding-top: 2rem;
}

.hero__img {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    background: #9ea8ba;
}

.hero__img--530 {
    height: 530px;
}

.hero__img--330 {
    height: 100%;
}

.hero__img--350 {
    height: 350px;
}

.hero--h-xs-375 {
    height: 220px;
}

@media (min-width: 576px) {
    .hero--h-xs-375 {
        height: 375px;
    }
}

.hero--h-xs-430 {
    height: 220px;
}

@media (min-width: 576px) {
    .hero--h-xs-430 {
        height: 430px;
    }
}

.hero--h-xs-480 {
    height: 220px;
}

@media (min-width: 576px) {
    .hero--h-xs-480 {
        height: 480px;
    }
}

.hero--h-xs-480-ext {
    height: 220px;
}

@media (min-width: 576px) {
    .hero--h-xs-480-ext {
        height: 480px;
    }
}

.hero__text-block {
    padding: 0.9375rem 0.9375rem;
}

@media (min-width: 771px) {
    .hero__text-block {
        padding: 40px;
    }
}

.hero__item:hover .hero__img img {
    -webkit-transform: scale(1.05, 1.05);
    -ms-transform: scale(1.05, 1.05);
    transform: scale(1.05, 1.05);
}

.hero__item:hover a,
.hero__item:hover .alfa {
    text-decoration: underline;
}

.hero__item .hero__img {
    overflow: hidden;
    position: relative;
    z-index: 1;
}

.hero__item .hero__img:after { /* missing hero image gradient */
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    display: inline-block;
    background: linear-gradient(359.41deg, rgba(0, 0, 0, 0.6) -1.32%, rgba(0, 0, 0, 0) 88.34%);
}

.hero__item .hero__img img {
    -webkit-transition: -webkit-transform 400ms ease-out;
    transition: -webkit-transform 400ms ease-out;
    -o-transition: transform 400ms ease-out;
    transition: transform 400ms ease-out;
    transition: transform 400ms ease-out, -webkit-transform 400ms ease-out;
    will-change: transform;
    position: absolute; /* front-page hero element image aspect ratio fix */
}

.hero__item--half {
    position: relative;
    border-left: 0;
    width: 50%;
}

@media (min-width: 992px) {
    .hero__item--half {
        height: 50%;
        width: 100%;
    }
}

@media (max-width: 575px) {
    .hero__item--half .alfa {
        font-size: 16px;
        font-size: 1rem;
    }
}

.hero__text-block--absolute {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 2;
}

@media (min-width: 771px) {
    .hero__text-block--absolute {
        padding: 40px;
    }
}

.hero__text-block--absolute-left {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 2;
}

@media (max-width: 991px) {
    .hero__text-block--mobile-absolute {
        position: absolute;
        bottom: 0;
        left: 0;
        z-index: 2;
    }
}

.hero__logo {
    position: absolute;
    top: 15px;
    left: 15px;
    z-index: 3;
}

@media (max-width: 770px) {
    .hero__logo {
        top: 8px;
        left: 8px;
        width: 60px;
    }
}

@media (min-width: 993px) and (max-width: 1200px) {
    .hero__logo--ext {
        top: 8px;
        left: 8px;
        width: 90px;
    }
}


.slider-hero-three-columns .swiper-slide {
    height: 620px;
}

.letter__item {
    position: absolute;
    top: -20px;
    left: -1px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    background-color: #000000;
    width: 20px;
    height: 20px;
}

@media (min-width: 576px) {
    .letter__item {
        top: -35px;
        width: 35px;
        height: 35px;
    }
}

/*changed to black */
.letter__item svg {
    fill: #000;
    font-size: 12px;
    font-size: 0.75rem;
}

@media (min-width: 576px) {
    .letter__item svg {
        font-size: 19px;
        font-size: 1.1875rem;
    }
}

/* Mobile accordion styles for side-panel--filter - Custom multiselect inspired design */
@media (max-width: 767px) {
    .side-panel--filter {
        position: relative;
    }
    
    /* Ensure form elements inside accordion maintain proper styling and spacing */
    .side-panel--filter .filter-content-inner .box {
        margin-bottom: 0;
        border: none;
    }
    
    .side-panel--filter .filter-content-inner .form__field,
    .side-panel--filter .filter-content-inner .margin-top-2 {
        margin-bottom: 1.5rem;
    }
    
    .side-panel--filter .filter-content-inner .form__field:last-child,
    .side-panel--filter .filter-content-inner .margin-top-2:last-child {
        margin-bottom: 0;
    }
    
    /* Remove borders from internal boxes to avoid double borders */
    .side-panel--filter .filter-content-inner .box.box--border {
        border: none !important;
    }
}

@media (min-width: 992px) {
    .side-panel {
        width: 330px;
        max-width: 330px;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 330px;
        flex: 0 0 330px;
    }
}

@media (min-width: 992px) {
    .side-panel--400 {
        width: 330px;
        max-width: 330px;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 330px;
        flex: 0 0 330px;
    }
}

@media (min-width: 1200px) {
    .side-panel--400 {
        width: 400px;
        max-width: 400px;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 400px;
        flex: 0 0 400px;
    }
}

@media (min-width: 992px) {
    .side-panel--short {
        width: 330px;
        max-width: 330px;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 330px;
        flex: 0 0 330px;
    }
}

@media (min-width: 1200px) {
    .side-panel--short {
        width: 340px;
        max-width: 340px;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 340px;
        flex: 0 0 340px;
    }
}

@media (max-width: 991px) {
    .side-publication__item {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
    }
}

@media (max-width: 991px) {
    .side-publication__image-block {
        width: 125px;
        max-width: 125px;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 125px;
        flex: 0 0 125px;
    }
}

@media (max-width: 991px) {
    .side-publication__text-block {
        padding-top: 1rem;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-box-pack: justify;
        -ms-flex-pack: justify;
        justify-content: space-between;
        padding-left: 1.25rem;
        width: calc(100% - 125px);
        max-width: calc(100% - 125px);
        -webkit-box-flex: 0;
        -ms-flex: 0 0 calc(100% - 125px);
        flex: 0 0 calc(100% - 125px);
    }
}

@media (min-width: 992px) {
    .main-content {
        position: relative;
        z-index: 2;
        left: -1px;
        width: calc(100% - 330px);
        max-width: calc(100% - 330px);
        -webkit-box-flex: 0;
        -ms-flex: 0 0 calc(100% - 330px);
        flex: 0 0 calc(100% - 330px);
    }
}

@media (min-width: 992px) {
    .main-content--400 {
        width: calc(100% - 330px);
        max-width: calc(100% - 330px);
        -webkit-box-flex: 0;
        -ms-flex: 0 0 calc(100% - 330px);
        flex: 0 0 calc(100% - 330px);
    }
}

@media (min-width: 1200px) {
    .main-content--400 {
        width: calc(100% - 400px);
        max-width: calc(100% - 400px);
        -webkit-box-flex: 0;
        -ms-flex: 0 0 calc(100% - 400px);
        flex: 0 0 calc(100% - 400px);
    }
}

@media (min-width: 992px) {
    .main-content--short {
        width: calc(100% - 330px);
        max-width: calc(100% - 330px);
        -webkit-box-flex: 0;
        -ms-flex: 0 0 calc(100% - 330px);
        flex: 0 0 calc(100% - 330px);
    }
}

@media (min-width: 1200px) {
    .main-content--short {
        width: calc(100% - 340px);
        max-width: calc(100% - 340px);
        -webkit-box-flex: 0;
        -ms-flex: 0 0 calc(100% - 340px);
        flex: 0 0 calc(100% - 340px);
    }
}

.btn--detail {
    position: absolute;
    bottom: 1.5625rem;
    right: 1.5625rem;
    z-index: 6;
}

@media (max-width: 575px) {
    .btn--detail {
        bottom: 0.75rem;
        right: 0.75rem;
        font-size: 12px;
        font-size: 0.75rem;
        min-height: 30px;
        min-width: 140px;
    }
}

.detail-hero {
    height: 360px;
}

@media (min-width: 576px) {
    .detail-hero {
        margin: 0;
        height: auto;
    }
}

.detail-hero--no-height {
    height: auto;
}

.detail-hero--small {
    height: 220px;
}

@media (min-width: 576px) {
    .detail-hero--small {
        height: auto;
    }
}

.detail-list--small .detail-list__left-panel {
    min-width: 110px;
}

.detail-list__item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 0.5rem;
}

.detail-list__left-panel {
    font-family: "SetupGrotesk-Medium", Arial, sans-serif;
    font-weight: 500;
    font-size: 14px;
    font-size: 0.875rem;
    width: 120px;
    flex-shrink: 0;
    margin-right: 0.625rem;
}

@media (min-width: 576px) {
    .detail-list__left-panel {
        font-size: 16px;
        font-size: 1rem;
        width: 155px;
    }
}

.detail-list__right-panel {
    font-family: "SetupGrotesk-Regular", Arial, sans-serif;
    font-weight: 400;
    -webkit-box-flex: 1;
    -ms-flex: auto;
    flex: auto;
    font-size: 14px;
    font-size: 0.875rem;
    align-self: flex-end;
}

@media (min-width: 576px) {
    .detail-list__right-panel {
        font-size: 16px;
        font-size: 1rem;
    }
}

.detail-list__right-panel a {
    line-height: 1;
    text-decoration: underline;
    display: inline-flex;
}

.detail-list__right-panel a:not(:last-child) {
    margin-right: 0.5rem;
    padding-right: 0.5rem;
    border-right: 1px solid rgba(111, 111, 111, 0.5);
}

.author {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

.author:hover .author__image-block img {
    -webkit-transform: scale(1.05, 1.05);
    -ms-transform: scale(1.05, 1.05);
    transform: scale(1.05, 1.05);
}

@media (min-width: 576px) {
    .author--xs-big .author__image-block {
        width: 56px;
        max-width: 56px;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 56px;
        flex: 0 0 56px;
        height: 56px;
    }
}

.author--xs-big .author__text {
    line-height: 1.4;
}

@media (min-width: 576px) {
    .author--xs-big .author__text {
        font-size: 16px;
        font-size: 1rem;
    }
}

.author-snippet {
    height: 32px;
}

.author__image-block {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 32px;
    max-width: 32px;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 32px;
    flex: 0 0 32px;
    height: 32px;
    border-radius: 100%;
    -webkit-mask-image: -webkit-radial-gradient(#fff, #000); /* round corners safari fix */
    overflow: hidden;
    margin-right: 0.625rem;
}

.author__image-block img {
    -webkit-transition: -webkit-transform 400ms ease-out;
    transition: -webkit-transform 400ms ease-out;
    -o-transition: transform 400ms ease-out;
    transition: transform 400ms ease-out;
    transition: transform 400ms ease-out, -webkit-transform 400ms ease-out;
    will-change: transform;
}

.author__text {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    margin-bottom: 0.5rem;
    line-height: 1;
    font-weight: 400;
    font-size: 14px;
    font-size: 0.875rem;
    text-decoration: underline;
}

span.author__text {
    text-decoration: none;
}

@media (min-width: 576px) {
    .author__text {
        font-size: 16px;
        font-size: 1rem;
    }
}

.author__text:not(:last-child) {
    margin-right: 0.7rem;
    padding-right: 0.7rem;
    border-right: 1px solid rgba(111, 111, 111, 0.5);
}

@media (min-width: 576px) {
    .author__text--big {
        font-size: 16px;
        font-size: 1rem;
    }
}

@media (min-width: 771px) {
    .author__text--big {
        margin-right: 0 !important;
        border-right: 0 !important;
    }
}

@media (min-width: 576px) {
    .masonry-group {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        margin: 0 -15px;
    }

    .masonry-group .card__item {
        margin-bottom: 1.875rem;
    }
}

@media (min-width: 576px) and (min-width: 576px) {
    .masonry-group .card__item {
        padding: 0 15px;
    }
}

.masonry__column {
    width: 100%;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
}

@media (min-width: 576px) {
    .masonry__column {
        width: 50%;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
    }
}

@media (min-width: 992px) {
    .masonry__column {
        width: 33.33%;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 33.33%;
        flex: 0 0 33.33%;
    }
}

.header-offer {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

@media (min-width: 993px) and (max-width: 1200px) {
    .header-offer {
        width: 220px;
    }
}

.header-offer__image-block {
    display: block;
    height: 60px;
    width: 60px;
    max-width: 60px;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 60px;
    flex: 0 0 60px;
}

@media (min-width: 993px) and (max-width: 1200px) {
    .header-offer__image-block {
        height: 40px;
        width: 40px;
        max-width: 40px;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 40px;
        flex: 0 0 40px;
    }
}

.header-offer__text-block {
    display: block;
    padding-left: 1rem;
    width: calc(100% - 60px);
    max-width: calc(100% - 60px);
    -webkit-box-flex: 0;
    -ms-flex: 0 0 calc(100% - 60px);
    flex: 0 0 calc(100% - 60px);
}

@media (min-width: 993px) and (max-width: 1200px) {
    .header-offer__text-block {
        width: calc(100% - 40px);
        max-width: calc(100% - 40px);
        -webkit-box-flex: 0;
        -ms-flex: 0 0 calc(100% - 40px);
        flex: 0 0 calc(100% - 40px);
    }
}

.header-offer__text {
    font-size: 12px; /* fix designum blok font size */
    font-size: 0.75rem; /* fix designum blok font size */
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 0.25rem;
    line-height: 1;
}

@media (min-width: 993px) and (max-width: 1200px) {
    .header-offer__text {
        font-size: 12px;
        font-size: 0.75rem;
    }
}

/* fix designum blok font size */
@media (min-width: 1440px) {
    .header-offer__text {
        font-size: 14px;
        font-size: 0.875rem;
    }
}

.header-offer__title {
    font-family: "SetupGrotesk-Bold", Arial, sans-serif;
    font-weight: 700;
    font-size: 16px; /* fix designum blok font size */
    font-size: 1rem; /* fix designum blok font size */
    color: white;
    margin-bottom: 0.4rem;
    line-height: 1;
}

@media (min-width: 993px) and (max-width: 1200px) {
    .header-offer__title {
        margin-bottom: 0;
        font-size: 14px;
        font-size: 0.875rem;
    }
}

/* fix designum blok font size */
@media (min-width: 1440px) {
    .header-offer__title {
        font-size: 18px;
        font-size: 1.125rem;
    }
}

.header-offer__description {
    font-size: 12px; /* fix designum blok font size */
    font-size: 0.75rem; /* fix designum blok font size */
    color: white;
    line-height: 1;
}

@media (min-width: 993px) and (max-width: 1200px) {
    .header-offer__description {
        display: none;
    }
}

/* fix designum blok font size */
@media (min-width: 1440px) {
    .header-offer__description {
        font-size: 14px;
        font-size: 0.875rem;
    }
}

.search-block {
    position: relative;
}

@media (max-width: 991px) {
    .search-block {
        position: absolute;
        right: 65px;
        top: 0;
        z-index: 99;
    }
}

.search__content {
    position: absolute;
    top: 0;
    left: 0;
    width: 340px;
    background: black;
    padding: 5px;
    background: #000000;
    border-radius: 4px;
}

@media (max-width: 991px) {
    .search__content {
        position: fixed;
        top: 0;
        width: 100%;
        height: calc(100% - 70px) !important;
        overflow: auto;
        padding: 15px 15px;
    }
}

@media (min-width: 1200px) {
    .search__content {
        width: 475px;
    }
}

.search__content.is-active {
    overflow-y: scroll;
}

.search__icon {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    cursor: pointer;
    width: 40px;
    height: 70px;
}

@media (min-width: 992px) {
    .search__icon {
        width: 40px;
        height: 35px;
    }
}

.search__icon svg {
    fill: #ffffff;
    font-size: 24px;
    font-size: 1.5rem;
}

@media (min-width: 992px) {
    .search__icon svg {
        font-size: 16px;
        font-size: 1rem;
    }
}

.search__content input {
    height: 40px;
    border-radius: 4px;
    padding-left: 40px;
    font-size: 16px;
    font-size: 1rem;
}

.search__icon--submit {
    position: absolute;
    top: 20px;
    left: 20px;
    height: 30px;
    width: 30px;
    border: 0;
    background: transparent;
}

@media (min-width: 992px) {
    .search__icon--submit {
        top: 10px;
        left: 10px;
    }
}

.search__icon--submit svg {
    fill: #000000;
}

.secondary-block {
    position: fixed;
    left: 0;
    width: 100%;
    background: #1b1b1b;
    z-index: 99;
    -webkit-transition: top 350ms ease;
    -o-transition: top 350ms ease;
    transition: top 350ms ease;
    bottom: 70px;
    display: none;
}

@media (min-width: 992px) {
    .secondary-block {
        display: block;
        top: 70px;
        bottom: auto;
    }

    body.kiosk .secondary-block {
        top: 0;
    }
}

.secondary-block.is-scrolling {
    top: 70px;
}

.secondary-menu {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

@media (min-width: 992px) {
    .secondary-menu {
        padding-left: 185px;
    }
}

@media (min-width: 1200px) {
    .secondary-menu {
        padding-left: 185px;
    }
}

@media (min-width: 1440px) {
    .secondary-menu {
        padding-left: 186px;
    }
}

@media (max-width: 991px) {
    .secondary-menu {
        width: 100%;
        overflow-y: hidden;
        overflow-x: auto;
        white-space: nowrap;
    }
}

.secondary-menu__item {
    margin-right: 20px;
}

@media (min-width: 992px) {
    .secondary-menu__item {
        margin-right: 25px;
    }
}

@media (min-width: 1200px) {
    .secondary-menu__item {
        margin-right: 35px;
    }
}

@media (max-width: 991px) {
    .secondary-menu__item {
        display: inline-block;
        vertical-align: middle;
    }
}

.secondary-menu__item.is-active .secondary-menu__link {
    text-decoration: underline;
}

.secondary-menu__link {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: start;
    height: 45px;
    color: #ffffff;
    font-family: "SetupGrotesk-Medium", Arial, sans-serif;
    font-weight: 500;
    font-size: 14px;
    font-size: 0.875rem;
}

@media (min-width: 771px) {
    .secondary-menu__link {
        font-size: 14px;
        font-size: 0.875rem;
    }
}

@media (min-width: 992px) {
    .secondary-menu__link {
        height: 70px;
        font-size: 14px;
        font-size: 0.875rem;
    }
}

@media (min-width: 1200px) {
    .secondary-menu__link {
        font-size: 16px;
        font-size: 1rem;
    }
}

@media (min-width: 993px) and (max-width: 1200px) {
    .secondary-menu__link {
        font-size: 14px;
        font-size: 0.875rem;
    }
}

.scroll-icon.is-disabled {
    padding-right: 0;
}

.scroll-icon.is-disabled:before, .scroll-icon.is-disabled:after {
    display: none;
}

.scroll-icon:before {
    content: '';
    display: none;
    position: absolute;
    top: 0;
    height: 45px;
    right: 0;
    width: 50px;
    text-align: center;
    background: #1b1b1b;
    z-index: 2;
}

@media (max-width: 770px) {
    .scroll-icon:before {
        display: block;
    }
}

.scroll-icon:after {
    content: '';
    display: none;
    position: absolute;
    top: calc(50% + 10px);
    margin-top: -25px;
    right: 5px;
    -webkit-animation: slideOutLeft 2s infinite;
    animation: slideOutLeft 2s infinite;
    z-index: 3;
    width: 19px;
    height: 26px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    background-image: url("/wp-content/themes/scd/assets/icons/scroll.svg");
}

@media (max-width: 770px) {
    .scroll-icon:after {
        display: block;
    }
}

.scroll-icon-category:before {
    width: 40px;
    height: 40px;
    background-color: transparent;
}

.scroll-icon-category:after {
    right: 0;
}

@media (max-width: 991px) {
    .scroll-md-icon:before {
        display: block;
        right: 0;
    }

    .scroll-md-icon .detail-menu-list-item:last-child {
        padding-right: 20px;
    }

    .scroll-md-icon:after {
        display: block;
        right: 5px;
    }
}

/* fix sirka textoveho bloku */
@media (min-width: 770px) {
    .w-624px {
        max-width: 74.32%;
        flex-basis: 74.32%;
    }
}

/* fix pretekania +/- tlacidiel na leaflet mape */
.leaflet-top, .leaflet-bottom {
    z-index: 400;
}

.icon-svg.icon-youtube {
    width: auto;
}

/* fix tag box top border unaligned with parent container */
.tag-list--offset-top .tag-list__link {
    border-top: 0;
    display: block;
}

/* add missing link underline on mini homepages */
.quicklink .card__content .card__title a {
    text-decoration: underline;
}

/* fix mobile menu bar overlaying disclaimer section in the footer */
@media (max-width: 991px) {
    .footer {
        padding-bottom: 70px;
    }
}

.js-load-more-grid.row > p {
    margin-left: 15px;
    margin-right: 15px;
}

@media (min-width: 1200px) {
    .js-load-more-grid.gutter-lg-60.row > p {
        margin-left: 30px;
        margin-right: 30px;
    }
}

.author.meta-info:not(:first-child) .author__text {
    padding-left: 15px;
    border-left: 1px solid rgba(111, 111, 111, 0.5);
}

/* Mobile grid layout - 2 products per row */
@media (max-width: 575px) {
    .js-load-more-grid .card__item {
        width: 50% !important;
        max-width: 50% !important;
        flex: 0 0 50% !important;
    }
}

/* Expandable description functionality */
.description-expandable {
    position: relative;
}

.description-expandable__content {
    max-height: 352px;
    overflow: hidden;
    -webkit-transition: max-height 0.4s ease;
    -o-transition: max-height 0.4s ease;
    transition: max-height 0.4s ease;
}

.description-expandable__content.is-expanded {
    max-height: 5000px;
    overflow: visible;
}

.description-expandable__toggle {
    display: inline-block;
    margin-top: 10px;
    color: #000;
    text-decoration: underline;
    cursor: pointer;
    font-weight: 500;
    -webkit-transition: opacity 0.3s ease;
    -o-transition: opacity 0.3s ease;
    transition: opacity 0.3s ease;
}

.description-expandable__toggle:hover {
    opacity: 0.7;
}

