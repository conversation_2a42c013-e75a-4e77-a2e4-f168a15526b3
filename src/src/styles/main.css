@import "tailwindcss";

@source "../../**/*.php";
@source "../../**/*.js";


.letter__item.svg-white svg {
    @apply fill-white;
}


.wrap-dropdown .content-dropdown {
    @apply h-0 overflow-hidden transition-all duration-300 ease-in-out md:h-auto;
}

.wrap-dropdown.active .content-dropdown {
    @apply h-auto;
}

.wrap-dropdown .header-dropdown svg {
    @apply transition-transform duration-300 ease-in-out w-5 h-auto;
}

.wrap-dropdown.active .header-dropdown svg {
    @apply rotate-180;
}

.swiper-arrow-custom {
    @apply flex gap-3.5;
}

.swiper-arrow-custom button {
    @apply flex items-center justify-center w-8 h-8 p-1 bg-black;
}

.swiper-arrow-custom button svg {
    @apply w-4 h-4 fill-white;
}

.swiper-arrow-custom button:disabled {
    @apply bg-black/25;
}

.swiper-arrow-custom .swiper-prev svg {
    @apply rotate-180;
}

.custom-multiselect {
    position: relative;
    width: 100%;
    border: 1px solid black;
    background: white;
    cursor: pointer;
    min-height: 44px;
}

.custom-multiselect__display {
    display: flex;
    align-items: center;
    padding: 8px 40px 8px 12px;
    min-height: 44px;
    position: relative;
}

.custom-multiselect__display:after {
    content: '' !important;
    width: 14px;
    height: 10px;
    display: block;
    position: absolute;
    top: 50%;
    right: 12px;
    -webkit-transform: translate(0, -50%);
    -ms-transform: translate(0, -50%);
    transform: translate(0, -50%);
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    background-image: url("/wp-content/themes/scd/assets/icons/chevron-down.svg");
    transition: transform 0.2s ease;
}

/* Arrow rotation when dropdown is open */
.custom-multiselect.open .custom-multiselect__display:after {
    transform: translate(0, -50%) rotate(180deg);
}

.custom-multiselect__placeholder {
    color: black;
    flex-grow: 1;
}

.custom-multiselect__selected-items {
    flex-grow: 1;
    color: black;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.custom-multiselect__counter {
    color: black;
    font-weight: bold;
    margin-left: 8px;
    flex-shrink: 0;
    white-space: nowrap;
    display: none !important; /* Temporarily hidden */
}

.custom-multiselect__selected-item {
    background: #007cba;
    color: white;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.custom-multiselect__selected-item .remove {
    cursor: pointer;
    font-weight: bold;
}


.custom-multiselect__dropdown {
    position: absolute;
    top: 100%;
    left: -1px;
    width: calc(100% + 2px);
    background: white;
    border: 1px solid black;
    border-top: none;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    box-sizing: border-box;
}

.custom-multiselect__option {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    gap: 8px;
    transition: background-color 0.2s;
    flex-direction: row-reverse;
}

.custom-multiselect__option:hover {
    background-color: #f5f5f5;
}

.custom-multiselect__checkbox {
    width: 16px;
    height: 16px;
    display: inline-block;
    position: relative;
    flex-shrink: 0;
    background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iMC41IiB5PSIwLjUiIHdpZHRoPSIxNSIgaGVpZ2h0PSIxNSIgc3Ryb2tlPSJibGFjayIvPgo8L3N2Zz4K");
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}

.custom-multiselect__option.selected .custom-multiselect__checkbox {
    background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iMC41IiB5PSIwLjUiIHdpZHRoPSIxNSIgaGVpZ2h0PSIxNSIgc3Ryb2tlPSJibGFjayIvPgo8cGF0aCBkPSJNNCA4LjE0Mjg2TDYuNjYwMSAxMUwxMS43MTQzIDUiIHN0cm9rZT0iYmxhY2siLz4KPC9zdmc+Cg==');
}

.custom-multiselect__text {
    flex-grow: 1;
    color: black;
}
